
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON Diff 可视化对比</title>

    
    <style>
         
        .jsondiffpatch-delta {
            font-family: 'Bitstream Vera Sans Mono', 'DejaVu Sans Mono', Monaco, Courier, monospace;
            font-size: 12px;
            margin: 0;
            padding: 0 0 0 12px;
            display: inline-block;
        }

        .jsondiffpatch-delta pre {
            font-family: 'Bitstream Vera Sans Mono', 'DejaVu Sans Mono', Monaco, Courier, monospace;
            font-size: 12px;
            margin: 0;
            padding: 0;
            display: inline-block;
        }

        ul.jsondiffpatch-delta {
            list-style-type: none;
            padding: 0 0 0 20px;
            margin: 0;
        }

        .jsondiffpatch-delta li {
            list-style-type: none;
        }

        .jsondiffpatch-added .jsondiffpatch-property-name,
        .jsondiffpatch-added .jsondiffpatch-value pre,
        .jsondiffpatch-added .jsondiffpatch-value {
            background: #bbffbb;
        }

        .jsondiffpatch-modified .jsondiffpatch-property-name,
        .jsondiffpatch-modified .jsondiffpatch-value pre,
        .jsondiffpatch-modified .jsondiffpatch-value {
            background: #ffffbb;
        }

        .jsondiffpatch-deleted .jsondiffpatch-property-name,
        .jsondiffpatch-deleted .jsondiffpatch-value pre,
        .jsondiffpatch-deleted .jsondiffpatch-value {
            background: #ffbbbb;
        }

        .jsondiffpatch-unchanged {
            color: gray;
        }

        .jsondiffpatch-movedestination {
            background: #ffffbb;
            color: #888;
        }

        .jsondiffpatch-movedestination::before {
            content: "(moved from ";
        }

        .jsondiffpatch-movedestination::after {
            content: ")";
        }

        .jsondiffpatch-moved .jsondiffpatch-value {
            background: #ffffbb;
        }

        .jsondiffpatch-moved .jsondiffpatch-property-name {
            color: #aaa;
            background: #ffffbb;
        }

        .jsondiffpatch-moved .jsondiffpatch-property-name::after {
            content: " (moved)";
        }

         
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .stat-total .stat-number { color: #667eea; }
        .stat-create .stat-number { color: #28a745; }
        .stat-update .stat-number { color: #ffc107; }
        .stat-delete .stat-number { color: #dc3545; }
        
        .diff-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            overflow: hidden;
            padding: 20px;
        }

        .no-diff {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .no-diff-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .no-diff h3 {
            font-size: 24px;
            margin-bottom: 10px;
            color: #333;
        }

        .diff-viewer {
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: white;
            overflow: auto;
            max-height: 80vh;
        }

        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .btn:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }

        .btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }
            
            .diff-change {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .diff-path {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 JSON Diff 可视化对比</h1>
            <p>清晰展示两个JSON版本之间的差异变化</p>
        </div>
        
        <div class="stats">
            <div class="stat-card stat-total">
                <div class="stat-number">1</div>
                <div class="stat-label">总差异项</div>
            </div>
            <div class="stat-card stat-create">
                <div class="stat-number">0</div>
                <div class="stat-label">新增</div>
            </div>
            <div class="stat-card stat-update">
                <div class="stat-number">1</div>
                <div class="stat-label">修改</div>
            </div>
            <div class="stat-card stat-delete">
                <div class="stat-number">0</div>
                <div class="stat-label">删除</div>
            </div>
        </div>
        
        <div class="diff-container">
            
                <div class="controls">
                    <button class="btn active" onclick="showVisualDiff()">可视化对比</button>
                    <button class="btn" onclick="showRawData()">原始数据</button>
                </div>

                <div id="visual-diff" class="diff-viewer">
                    
                </div>

                <div id="raw-data" class="diff-viewer" style="display: none;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; height: 100%;">
                        <div>
                            <h4 style="padding: 10px; background: #f8f9fa; margin: 0; border-bottom: 1px solid #dee2e6;">原始数据</h4>
                            <pre style="padding: 20px; margin: 0; overflow: auto; height: calc(100% - 40px); background: #f8f9fa;">{&#34;groupList&#34;:[{&#34;element&#34;:[{&#34;ItemId&#34;:4,&#34;ItemName&#34;:&#34;蜂鸟预约问卷&#34;,&#34;isLight&#34;:0}],&#34;groupId&#34;:2,&#34;groupName&#34;:&#34;营销&#34;,&#34;isLight&#34;:0},{&#34;element&#34;:[{&#34;ItemId&#34;:5,&#34;ItemName&#34;:&#34;到课(0/2)&#34;,&#34;isLight&#34;:0}],&#34;groupId&#34;:3,&#34;groupName&#34;:&#34;到课&#34;,&#34;isLight&#34;:0},{&#34;element&#34;:[{&#34;ItemId&#34;:6,&#34;ItemName&#34;:&#34;完课(0/2)&#34;,&#34;isLight&#34;:0}],&#34;groupId&#34;:4,&#34;groupName&#34;:&#34;完课&#34;,&#34;isLight&#34;:0},{&#34;element&#34;:[{&#34;ItemId&#34;:7,&#34;ItemName&#34;:&#34;回放(0/2)&#34;,&#34;isLight&#34;:0}],&#34;groupId&#34;:5,&#34;groupName&#34;:&#34;回放&#34;,&#34;isLight&#34;:0}]}</pre>
                        </div>
                        <div>
                            <h4 style="padding: 10px; background: #f8f9fa; margin: 0; border-bottom: 1px solid #dee2e6;">新数据</h4>
                            <pre style="padding: 20px; margin: 0; overflow: auto; height: calc(100% - 40px); background: #f8f9fa;">{&#34;groupList&#34;:[{&#34;element&#34;:[{&#34;ItemId&#34;:7,&#34;ItemName&#34;:&#34;回放(0/2)&#34;,&#34;isLight&#34;:0}],&#34;groupId&#34;:5,&#34;groupName&#34;:&#34;回放&#34;,&#34;isLight&#34;:0},{&#34;element&#34;:[{&#34;ItemId&#34;:4,&#34;ItemName&#34;:&#34;蜂鸟预约问卷&#34;,&#34;isLight&#34;:0}],&#34;groupId&#34;:2,&#34;groupName&#34;:&#34;营销&#34;,&#34;isLight&#34;:0},{&#34;element&#34;:[{&#34;ItemId&#34;:5,&#34;ItemName&#34;:&#34;到课(0/2)&#34;,&#34;isLight&#34;:0}],&#34;groupId&#34;:3,&#34;groupName&#34;:&#34;到课&#34;,&#34;isLight&#34;:0},{&#34;element&#34;:[{&#34;ItemId&#34;:6,&#34;ItemName&#34;:&#34;完课(0/2)&#34;,&#34;isLight&#34;:0}],&#34;groupId&#34;:4,&#34;groupName&#34;:&#34;完课&#34;,&#34;isLight&#34;:0}]}</pre>
                        </div>
                    </div>
                </div>
            
        </div>
    </div>

    
    
    <script>"(function(global,factory){typeof exports==='object'\u0026\u0026typeof module!=='undefined'?factory(exports,require('./empty')):typeof define==='function'\u0026\u0026define.amd?define(['exports','./empty'],factory):(factory((global.jsondiffpatch={}),global.chalk));}(this,(function(exports,chalk){'use strict';chalk=chalk\u0026\u0026chalk.hasOwnProperty('default')?chalk['default']:chalk;var _typeof=typeof Symbol===\"function\"\u0026\u0026typeof Symbol.iterator===\"symbol\"?function(obj){return typeof obj;}:function(obj){return obj\u0026\u0026typeof Symbol===\"function\"\u0026\u0026obj.constructor===Symbol\u0026\u0026obj!==Symbol.prototype?\"symbol\":typeof obj;};var classCallCheck=function(instance,Constructor){if(!(instance instanceof Constructor)){throw new TypeError(\"Cannot call a class as a function\");}};var createClass=function(){function defineProperties(target,props){for(var i=0;i\u003cprops.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||false;descriptor.configurable=true;if(\"value\"in descriptor)descriptor.writable=true;Object.defineProperty(target,descriptor.key,descriptor);}}return function(Constructor,protoProps,staticProps){if(protoProps)defineProperties(Constructor.prototype,protoProps);if(staticProps)defineProperties(Constructor,staticProps);return Constructor;};}();var get=function get(object,property,receiver){if(object===null)object=Function.prototype;var desc=Object.getOwnPropertyDescriptor(object,property);if(desc===undefined){var parent=Object.getPrototypeOf(object);if(parent===null){return undefined;}else{return get(parent,property,receiver);}}else if(\"value\"in desc){return desc.value;}else{var getter=desc.get;if(getter===undefined){return undefined;}return getter.call(receiver);}};var inherits=function(subClass,superClass){if(typeof superClass!==\"function\"\u0026\u0026superClass!==null){throw new TypeError(\"Super expression must either be null or a function, not \"+typeof superClass);}subClass.prototype=Object.create(superClass\u0026\u0026superClass.prototype,{constructor:{value:subClass,enumerable:false,writable:true,configurable:true}});if(superClass)Object.setPrototypeOf?Object.setPrototypeOf(subClass,superClass):subClass.__proto__=superClass;};var possibleConstructorReturn=function(self,call){if(!self){throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");}return call\u0026\u0026(typeof call===\"object\"||typeof call===\"function\")?call:self;};var slicedToArray=function(){function sliceIterator(arr,i){var _arr=[];var _n=true;var _d=false;var _e=undefined;try{for(var _i=arr[Symbol.iterator](),_s;!(_n=(_s=_i.next()).done);_n=true){_arr.push(_s.value);if(i\u0026\u0026_arr.length===i)break;}}catch(err){_d=true;_e=err;}finally{try{if(!_n\u0026\u0026_i[\"return\"])_i[\"return\"]();}finally{if(_d)throw _e;}}return _arr;}return function(arr,i){if(Array.isArray(arr)){return arr;}else if(Symbol.iterator in Object(arr)){return sliceIterator(arr,i);}else{throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");}};}();var toConsumableArray=function(arr){if(Array.isArray(arr)){for(var i=0,arr2=Array(arr.length);i\u003carr.length;i++)arr2[i]=arr[i];return arr2;}else{return Array.from(arr);}};var Processor=function(){function Processor(options){classCallCheck(this,Processor);this.selfOptions=options||{};this.pipes={};}createClass(Processor,[{key:'options',value:function options(_options){if(_options){this.selfOptions=_options;}return this.selfOptions;}},{key:'pipe',value:function pipe(name,pipeArg){var pipe=pipeArg;if(typeof name==='string'){if(typeof pipe==='undefined'){return this.pipes[name];}else{this.pipes[name]=pipe;}}if(name\u0026\u0026name.name){pipe=name;if(pipe.processor===this){return pipe;}this.pipes[pipe.name]=pipe;}pipe.processor=this;return pipe;}},{key:'process',value:function process(input,pipe){var context=input;context.options=this.options();var nextPipe=pipe||input.pipe||'default';var lastPipe=void 0;var lastContext=void 0;while(nextPipe){if(typeof context.nextAfterChildren!=='undefined'){context.next=context.nextAfterChildren;context.nextAfterChildren=null;}if(typeof nextPipe==='string'){nextPipe=this.pipe(nextPipe);}nextPipe.process(context);lastContext=context;lastPipe=nextPipe;nextPipe=null;if(context){if(context.next){context=context.next;nextPipe=lastContext.nextPipe||context.pipe||lastPipe;}}}return context.hasResult?context.result:undefined;}}]);return Processor;}();var Pipe=function(){function Pipe(name){classCallCheck(this,Pipe);this.name=name;this.filters=[];}createClass(Pipe,[{key:'process',value:function process(input){if(!this.processor){throw new Error('add this pipe to a processor before using it');}var debug=this.debug;var length=this.filters.length;var context=input;for(var index=0;index\u003clength;index++){var filter=this.filters[index];if(debug){this.log('filter: '+filter.filterName);}filter(context);if((typeof context==='undefined'?'undefined':_typeof(context))==='object'\u0026\u0026context.exiting){context.exiting=false;break;}}if(!context.next\u0026\u0026this.resultCheck){this.resultCheck(context);}}},{key:'log',value:function log(msg){console.log('[jsondiffpatch] '+this.name+' pipe, '+msg);}},{key:'append',value:function append(){var _filters;(_filters=this.filters).push.apply(_filters,arguments);return this;}},{key:'prepend',value:function prepend(){var _filters2;(_filters2=this.filters).unshift.apply(_filters2,arguments);return this;}},{key:'indexOf',value:function indexOf(filterName){if(!filterName){throw new Error('a filter name is required');}for(var index=0;index\u003cthis.filters.length;index++){var filter=this.filters[index];if(filter.filterName===filterName){return index;}}throw new Error('filter not found: '+filterName);}},{key:'list',value:function list(){return this.filters.map(function(f){return f.filterName;});}},{key:'after',value:function after(filterName){var index=this.indexOf(filterName);var params=Array.prototype.slice.call(arguments,1);if(!params.length){throw new Error('a filter is required');}params.unshift(index+1,0);Array.prototype.splice.apply(this.filters,params);return this;}},{key:'before',value:function before(filterName){var index=this.indexOf(filterName);var params=Array.prototype.slice.call(arguments,1);if(!params.length){throw new Error('a filter is required');}params.unshift(index,0);Array.prototype.splice.apply(this.filters,params);return this;}},{key:'replace',value:function replace(filterName){var index=this.indexOf(filterName);var params=Array.prototype.slice.call(arguments,1);if(!params.length){throw new Error('a filter is required');}params.unshift(index,1);Array.prototype.splice.apply(this.filters,params);return this;}},{key:'remove',value:function remove(filterName){var index=this.indexOf(filterName);this.filters.splice(index,1);return this;}},{key:'clear',value:function clear(){this.filters.length=0;return this;}},{key:'shouldHaveResult',value:function shouldHaveResult(should){if(should===false){this.resultCheck=null;return;}if(this.resultCheck){return;}var pipe=this;this.resultCheck=function(context){if(!context.hasResult){console.log(context);var error=new Error(pipe.name+' failed');error.noResult=true;throw error;}};return this;}}]);return Pipe;}();var Context=function(){function Context(){classCallCheck(this,Context);}createClass(Context,[{key:'setResult',value:function setResult(result){this.result=result;this.hasResult=true;return this;}},{key:'exit',value:function exit(){this.exiting=true;return this;}},{key:'switchTo',value:function switchTo(next,pipe){if(typeof next==='string'||next instanceof Pipe){this.nextPipe=next;}else{this.next=next;if(pipe){this.nextPipe=pipe;}}return this;}},{key:'push',value:function push(child,name){child.parent=this;if(typeof name!=='undefined'){child.childName=name;}child.root=this.root||this;child.options=child.options||this.options;if(!this.children){this.children=[child];this.nextAfterChildren=this.next||null;this.next=child;}else{this.children[this.children.length-1].next=child;this.children.push(child);}child.next=this;return this;}}]);return Context;}();var isArray=typeof Array.isArray==='function'?Array.isArray:function(a){return a instanceof Array;};function cloneRegExp(re){var regexMatch=/^\\/(.*)\\/([gimyu]*)$/.exec(re.toString());return new RegExp(regexMatch[1],regexMatch[2]);}function clone(arg){if((typeof arg==='undefined'?'undefined':_typeof(arg))!=='object'){return arg;}if(arg===null){return null;}if(isArray(arg)){return arg.map(clone);}if(arg instanceof Date){return new Date(arg.getTime());}if(arg instanceof RegExp){return cloneRegExp(arg);}var cloned={};for(var name in arg){if(Object.prototype.hasOwnProperty.call(arg,name)){cloned[name]=clone(arg[name]);}}return cloned;}var DiffContext=function(_Context){inherits(DiffContext,_Context);function DiffContext(left,right){classCallCheck(this,DiffContext);var _this=possibleConstructorReturn(this,(DiffContext.__proto__||Object.getPrototypeOf(DiffContext)).call(this));_this.left=left;_this.right=right;_this.pipe='diff';return _this;}createClass(DiffContext,[{key:'setResult',value:function setResult(result){if(this.options.cloneDiffValues\u0026\u0026(typeof result==='undefined'?'undefined':_typeof(result))==='object'){var clone$$1=typeof this.options.cloneDiffValues==='function'?this.options.cloneDiffValues:clone;if(_typeof(result[0])==='object'){result[0]=clone$$1(result[0]);}if(_typeof(result[1])==='object'){result[1]=clone$$1(result[1]);}}return Context.prototype.setResult.apply(this,arguments);}}]);return DiffContext;}(Context);var PatchContext=function(_Context){inherits(PatchContext,_Context);function PatchContext(left,delta){classCallCheck(this,PatchContext);var _this=possibleConstructorReturn(this,(PatchContext.__proto__||Object.getPrototypeOf(PatchContext)).call(this));_this.left=left;_this.delta=delta;_this.pipe='patch';return _this;}return PatchContext;}(Context);var ReverseContext=function(_Context){inherits(ReverseContext,_Context);function ReverseContext(delta){classCallCheck(this,ReverseContext);var _this=possibleConstructorReturn(this,(ReverseContext.__proto__||Object.getPrototypeOf(ReverseContext)).call(this));_this.delta=delta;_this.pipe='reverse';return _this;}return ReverseContext;}(Context);var isArray$1=typeof Array.isArray==='function'?Array.isArray:function(a){return a instanceof Array;};var diffFilter=function trivialMatchesDiffFilter(context){if(context.left===context.right){context.setResult(undefined).exit();return;}if(typeof context.left==='undefined'){if(typeof context.right==='function'){throw new Error('functions are not supported');}context.setResult([context.right]).exit();return;}if(typeof context.right==='undefined'){context.setResult([context.left,0,0]).exit();return;}if(typeof context.left==='function'||typeof context.right==='function'){throw new Error('functions are not supported');}context.leftType=context.left===null?'null':_typeof(context.left);context.rightType=context.right===null?'null':_typeof(context.right);if(context.leftType!==context.rightType){context.setResult([context.left,context.right]).exit();return;}if(context.leftType==='boolean'||context.leftType==='number'){context.setResult([context.left,context.right]).exit();return;}if(context.leftType==='object'){context.leftIsArray=isArray$1(context.left);}if(context.rightType==='object'){context.rightIsArray=isArray$1(context.right);}if(context.leftIsArray!==context.rightIsArray){context.setResult([context.left,context.right]).exit();return;}if(context.left instanceof RegExp){if(context.right instanceof RegExp){context.setResult([context.left.toString(),context.right.toString()]).exit();}else{context.setResult([context.left,context.right]).exit();}}};diffFilter.filterName='trivial';var patchFilter=function trivialMatchesPatchFilter(context){if(typeof context.delta==='undefined'){context.setResult(context.left).exit();return;}context.nested=!isArray$1(context.delta);if(context.nested){return;}if(context.delta.length===1){context.setResult(context.delta[0]).exit();return;}if(context.delta.length===2){if(context.left instanceof RegExp){var regexArgs=/^\\/(.*)\\/([gimyu]+)$/.exec(context.delta[1]);if(regexArgs){context.setResult(new RegExp(regexArgs[1],regexArgs[2])).exit();return;}}context.setResult(context.delta[1]).exit();return;}if(context.delta.length===3\u0026\u0026context.delta[2]===0){context.setResult(undefined).exit();}};patchFilter.filterName='trivial';var reverseFilter=function trivialReferseFilter(context){if(typeof context.delta==='undefined'){context.setResult(context.delta).exit();return;}context.nested=!isArray$1(context.delta);if(context.nested){return;}if(context.delta.length===1){context.setResult([context.delta[0],0,0]).exit();return;}if(context.delta.length===2){context.setResult([context.delta[1],context.delta[0]]).exit();return;}if(context.delta.length===3\u0026\u0026context.delta[2]===0){context.setResult([context.delta[0]]).exit();}};reverseFilter.filterName='trivial';function collectChildrenDiffFilter(context){if(!context||!context.children){return;}var length=context.children.length;var child=void 0;var result=context.result;for(var index=0;index\u003clength;index++){child=context.children[index];if(typeof child.result==='undefined'){continue;}result=result||{};result[child.childName]=child.result;}if(result\u0026\u0026context.leftIsArray){result._t='a';}context.setResult(result).exit();}collectChildrenDiffFilter.filterName='collectChildren';function objectsDiffFilter(context){if(context.leftIsArray||context.leftType!=='object'){return;}var name=void 0;var child=void 0;var propertyFilter=context.options.propertyFilter;for(name in context.left){if(!Object.prototype.hasOwnProperty.call(context.left,name)){continue;}if(propertyFilter\u0026\u0026!propertyFilter(name,context)){continue;}child=new DiffContext(context.left[name],context.right[name]);context.push(child,name);}for(name in context.right){if(!Object.prototype.hasOwnProperty.call(context.right,name)){continue;}if(propertyFilter\u0026\u0026!propertyFilter(name,context)){continue;}if(typeof context.left[name]==='undefined'){child=new DiffContext(undefined,context.right[name]);context.push(child,name);}}if(!context.children||context.children.length===0){context.setResult(undefined).exit();return;}context.exit();}objectsDiffFilter.filterName='objects';var patchFilter$1=function nestedPatchFilter(context){if(!context.nested){return;}if(context.delta._t){return;}var name=void 0;var child=void 0;for(name in context.delta){child=new PatchContext(context.left[name],context.delta[name]);context.push(child,name);}context.exit();};patchFilter$1.filterName='objects';var collectChildrenPatchFilter=function collectChildrenPatchFilter(context){if(!context||!context.children){return;}if(context.delta._t){return;}var length=context.children.length;var child=void 0;for(var index=0;index\u003clength;index++){child=context.children[index];if(Object.prototype.hasOwnProperty.call(context.left,child.childName)\u0026\u0026child.result===undefined){delete context.left[child.childName];}else if(context.left[child.childName]!==child.result){context.left[child.childName]=child.result;}}context.setResult(context.left).exit();};collectChildrenPatchFilter.filterName='collectChildren';var reverseFilter$1=function nestedReverseFilter(context){if(!context.nested){return;}if(context.delta._t){return;}var name=void 0;var child=void 0;for(name in context.delta){child=new ReverseContext(context.delta[name]);context.push(child,name);}context.exit();};reverseFilter$1.filterName='objects';function collectChildrenReverseFilter(context){if(!context||!context.children){return;}if(context.delta._t){return;}var length=context.children.length;var child=void 0;var delta={};for(var index=0;index\u003clength;index++){child=context.children[index];if(delta[child.childName]!==child.result){delta[child.childName]=child.result;}}context.setResult(delta).exit();}collectChildrenReverseFilter.filterName='collectChildren';var defaultMatch=function defaultMatch(array1,array2,index1,index2){return array1[index1]===array2[index2];};var lengthMatrix=function lengthMatrix(array1,array2,match,context){var len1=array1.length;var len2=array2.length;var x=void 0,y=void 0;var matrix=[len1+1];for(x=0;x\u003clen1+1;x++){matrix[x]=[len2+1];for(y=0;y\u003clen2+1;y++){matrix[x][y]=0;}}matrix.match=match;for(x=1;x\u003clen1+1;x++){for(y=1;y\u003clen2+1;y++){if(match(array1,array2,x-1,y-1,context)){matrix[x][y]=matrix[x-1][y-1]+1;}else{matrix[x][y]=Math.max(matrix[x-1][y],matrix[x][y-1]);}}}return matrix;};var backtrack=function backtrack(matrix,array1,array2,context){var index1=array1.length;var index2=array2.length;var subsequence={sequence:[],indices1:[],indices2:[]};while(index1!==0\u0026\u0026index2!==0){var sameLetter=matrix.match(array1,array2,index1-1,index2-1,context);if(sameLetter){subsequence.sequence.unshift(array1[index1-1]);subsequence.indices1.unshift(index1-1);subsequence.indices2.unshift(index2-1);--index1;--index2;}else{var valueAtMatrixAbove=matrix[index1][index2-1];var valueAtMatrixLeft=matrix[index1-1][index2];if(valueAtMatrixAbove\u003evalueAtMatrixLeft){--index2;}else{--index1;}}}return subsequence;};var get$1=function get(array1,array2,match,context){var innerContext=context||{};var matrix=lengthMatrix(array1,array2,match||defaultMatch,innerContext);var result=backtrack(matrix,array1,array2,innerContext);if(typeof array1==='string'\u0026\u0026typeof array2==='string'){result.sequence=result.sequence.join('');}return result;};var lcs={get:get$1};var ARRAY_MOVE=3;var isArray$2=typeof Array.isArray==='function'?Array.isArray:function(a){return a instanceof Array;};var arrayIndexOf=typeof Array.prototype.indexOf==='function'?function(array,item){return array.indexOf(item);}:function(array,item){var length=array.length;for(var i=0;i\u003clength;i++){if(array[i]===item){return i;}}return-1;};function arraysHaveMatchByRef(array1,array2,len1,len2){for(var index1=0;index1\u003clen1;index1++){var val1=array1[index1];for(var index2=0;index2\u003clen2;index2++){var val2=array2[index2];if(index1!==index2\u0026\u0026val1===val2){return true;}}}}function matchItems(array1,array2,index1,index2,context){var value1=array1[index1];var value2=array2[index2];if(value1===value2){return true;}if((typeof value1==='undefined'?'undefined':_typeof(value1))!=='object'||(typeof value2==='undefined'?'undefined':_typeof(value2))!=='object'){return false;}var objectHash=context.objectHash;if(!objectHash){return context.matchByPosition\u0026\u0026index1===index2;}var hash1=void 0;var hash2=void 0;if(typeof index1==='number'){context.hashCache1=context.hashCache1||[];hash1=context.hashCache1[index1];if(typeof hash1==='undefined'){context.hashCache1[index1]=hash1=objectHash(value1,index1);}}else{hash1=objectHash(value1);}if(typeof hash1==='undefined'){return false;}if(typeof index2==='number'){context.hashCache2=context.hashCache2||[];hash2=context.hashCache2[index2];if(typeof hash2==='undefined'){context.hashCache2[index2]=hash2=objectHash(value2,index2);}}else{hash2=objectHash(value2);}if(typeof hash2==='undefined'){return false;}return hash1===hash2;}var diffFilter$1=function arraysDiffFilter(context){if(!context.leftIsArray){return;}var matchContext={objectHash:context.options\u0026\u0026context.options.objectHash,matchByPosition:context.options\u0026\u0026context.options.matchByPosition};var commonHead=0;var commonTail=0;var index=void 0;var index1=void 0;var index2=void 0;var array1=context.left;var array2=context.right;var len1=array1.length;var len2=array2.length;var child=void 0;if(len1\u003e0\u0026\u0026len2\u003e0\u0026\u0026!matchContext.objectHash\u0026\u0026typeof matchContext.matchByPosition!=='boolean'){matchContext.matchByPosition=!arraysHaveMatchByRef(array1,array2,len1,len2);}while(commonHead\u003clen1\u0026\u0026commonHead\u003clen2\u0026\u0026matchItems(array1,array2,commonHead,commonHead,matchContext)){index=commonHead;child=new DiffContext(context.left[index],context.right[index]);context.push(child,index);commonHead++;}while(commonTail+commonHead\u003clen1\u0026\u0026commonTail+commonHead\u003clen2\u0026\u0026matchItems(array1,array2,len1-1-commonTail,len2-1-commonTail,matchContext)){index1=len1-1-commonTail;index2=len2-1-commonTail;child=new DiffContext(context.left[index1],context.right[index2]);context.push(child,index2);commonTail++;}var result=void 0;if(commonHead+commonTail===len1){if(len1===len2){context.setResult(undefined).exit();return;}result=result||{_t:'a'};for(index=commonHead;index\u003clen2-commonTail;index++){result[index]=[array2[index]];}context.setResult(result).exit();return;}if(commonHead+commonTail===len2){result=result||{_t:'a'};for(index=commonHead;index\u003clen1-commonTail;index++){result['_'+index]=[array1[index],0,0];}context.setResult(result).exit();return;}delete matchContext.hashCache1;delete matchContext.hashCache2;var trimmed1=array1.slice(commonHead,len1-commonTail);var trimmed2=array2.slice(commonHead,len2-commonTail);var seq=lcs.get(trimmed1,trimmed2,matchItems,matchContext);var removedItems=[];result=result||{_t:'a'};for(index=commonHead;index\u003clen1-commonTail;index++){if(arrayIndexOf(seq.indices1,index-commonHead)\u003c0){result['_'+index]=[array1[index],0,0];removedItems.push(index);}}var detectMove=true;if(context.options\u0026\u0026context.options.arrays\u0026\u0026context.options.arrays.detectMove===false){detectMove=false;}var includeValueOnMove=false;if(context.options\u0026\u0026context.options.arrays\u0026\u0026context.options.arrays.includeValueOnMove){includeValueOnMove=true;}var removedItemsLength=removedItems.length;for(index=commonHead;index\u003clen2-commonTail;index++){var indexOnArray2=arrayIndexOf(seq.indices2,index-commonHead);if(indexOnArray2\u003c0){var isMove=false;if(detectMove\u0026\u0026removedItemsLength\u003e0){for(var removeItemIndex1=0;removeItemIndex1\u003cremovedItemsLength;removeItemIndex1++){index1=removedItems[removeItemIndex1];if(matchItems(trimmed1,trimmed2,index1-commonHead,index-commonHead,matchContext)){result['_'+index1].splice(1,2,index,ARRAY_MOVE);if(!includeValueOnMove){result['_'+index1][0]='';}index2=index;child=new DiffContext(context.left[index1],context.right[index2]);context.push(child,index2);removedItems.splice(removeItemIndex1,1);isMove=true;break;}}}if(!isMove){result[index]=[array2[index]];}}else{index1=seq.indices1[indexOnArray2]+commonHead;index2=seq.indices2[indexOnArray2]+commonHead;child=new DiffContext(context.left[index1],context.right[index2]);context.push(child,index2);}}context.setResult(result).exit();};diffFilter$1.filterName='arrays';var compare={numerically:function numerically(a,b){return a-b;},numericallyBy:function numericallyBy(name){return function(a,b){return a[name]-b[name];};}};var patchFilter$2=function nestedPatchFilter(context){if(!context.nested){return;}if(context.delta._t!=='a'){return;}var index=void 0;var index1=void 0;var delta=context.delta;var array=context.left;var toRemove=[];var toInsert=[];var toModify=[];for(index in delta){if(index!=='_t'){if(index[0]==='_'){if(delta[index][2]===0||delta[index][2]===ARRAY_MOVE){toRemove.push(parseInt(index.slice(1),10));}else{throw new Error('only removal or move can be applied at original array indices,'+(' invalid diff type: '+delta[index][2]));}}else{if(delta[index].length===1){toInsert.push({index:parseInt(index,10),value:delta[index][0]});}else{toModify.push({index:parseInt(index,10),delta:delta[index]});}}}}toRemove=toRemove.sort(compare.numerically);for(index=toRemove.length-1;index\u003e=0;index--){index1=toRemove[index];var indexDiff=delta['_'+index1];var removedValue=array.splice(index1,1)[0];if(indexDiff[2]===ARRAY_MOVE){toInsert.push({index:indexDiff[1],value:removedValue});}}toInsert=toInsert.sort(compare.numericallyBy('index'));var toInsertLength=toInsert.length;for(index=0;index\u003ctoInsertLength;index++){var insertion=toInsert[index];array.splice(insertion.index,0,insertion.value);}var toModifyLength=toModify.length;var child=void 0;if(toModifyLength\u003e0){for(index=0;index\u003ctoModifyLength;index++){var modification=toModify[index];child=new PatchContext(context.left[modification.index],modification.delta);context.push(child,modification.index);}}if(!context.children){context.setResult(context.left).exit();return;}context.exit();};patchFilter$2.filterName='arrays';var collectChildrenPatchFilter$1=function collectChildrenPatchFilter(context){if(!context||!context.children){return;}if(context.delta._t!=='a'){return;}var length=context.children.length;var child=void 0;for(var index=0;index\u003clength;index++){child=context.children[index];context.left[child.childName]=child.result;}context.setResult(context.left).exit();};collectChildrenPatchFilter$1.filterName='arraysCollectChildren';var reverseFilter$2=function arraysReverseFilter(context){if(!context.nested){if(context.delta[2]===ARRAY_MOVE){context.newName='_'+context.delta[1];context.setResult([context.delta[0],parseInt(context.childName.substr(1),10),ARRAY_MOVE]).exit();}return;}if(context.delta._t!=='a'){return;}var name=void 0;var child=void 0;for(name in context.delta){if(name==='_t'){continue;}child=new ReverseContext(context.delta[name]);context.push(child,name);}context.exit();};reverseFilter$2.filterName='arrays';var reverseArrayDeltaIndex=function reverseArrayDeltaIndex(delta,index,itemDelta){if(typeof index==='string'\u0026\u0026index[0]==='_'){return parseInt(index.substr(1),10);}else if(isArray$2(itemDelta)\u0026\u0026itemDelta[2]===0){return'_'+index;}var reverseIndex=+index;for(var deltaIndex in delta){var deltaItem=delta[deltaIndex];if(isArray$2(deltaItem)){if(deltaItem[2]===ARRAY_MOVE){var moveFromIndex=parseInt(deltaIndex.substr(1),10);var moveToIndex=deltaItem[1];if(moveToIndex===+index){return moveFromIndex;}if(moveFromIndex\u003c=reverseIndex\u0026\u0026moveToIndex\u003ereverseIndex){reverseIndex++;}else if(moveFromIndex\u003e=reverseIndex\u0026\u0026moveToIndex\u003creverseIndex){reverseIndex--;}}else if(deltaItem[2]===0){var deleteIndex=parseInt(deltaIndex.substr(1),10);if(deleteIndex\u003c=reverseIndex){reverseIndex++;}}else if(deltaItem.length===1\u0026\u0026deltaIndex\u003c=reverseIndex){reverseIndex--;}}}return reverseIndex;};function collectChildrenReverseFilter$1(context){if(!context||!context.children){return;}if(context.delta._t!=='a'){return;}var length=context.children.length;var child=void 0;var delta={_t:'a'};for(var index=0;index\u003clength;index++){child=context.children[index];var name=child.newName;if(typeof name==='undefined'){name=reverseArrayDeltaIndex(context.delta,child.childName,child.result);}if(delta[name]!==child.result){delta[name]=child.result;}}context.setResult(delta).exit();}collectChildrenReverseFilter$1.filterName='arraysCollectChildren';var diffFilter$2=function datesDiffFilter(context){if(context.left instanceof Date){if(context.right instanceof Date){if(context.left.getTime()!==context.right.getTime()){context.setResult([context.left,context.right]);}else{context.setResult(undefined);}}else{context.setResult([context.left,context.right]);}context.exit();}else if(context.right instanceof Date){context.setResult([context.left,context.right]).exit();}};diffFilter$2.filterName='dates';function createCommonjsModule(fn,module){return module={exports:{}},fn(module,module.exports),module.exports;}var diffMatchPatch=createCommonjsModule(function(module){function diff_match_patch(){this.Diff_Timeout=1.0;this.Diff_EditCost=4;this.Match_Threshold=0.5;this.Match_Distance=1000;this.Patch_DeleteThreshold=0.5;this.Patch_Margin=4;this.Match_MaxBits=32;}var DIFF_DELETE=-1;var DIFF_INSERT=1;var DIFF_EQUAL=0;diff_match_patch.prototype.diff_main=function(text1,text2,opt_checklines,opt_deadline){if(typeof opt_deadline=='undefined'){if(this.Diff_Timeout\u003c=0){opt_deadline=Number.MAX_VALUE;}else{opt_deadline=(new Date).getTime()+this.Diff_Timeout*1000;}}var deadline=opt_deadline;if(text1==null||text2==null){throw new Error('Null input. (diff_main)');}if(text1==text2){if(text1){return[[DIFF_EQUAL,text1]];}return[];}if(typeof opt_checklines=='undefined'){opt_checklines=true;}var checklines=opt_checklines;var commonlength=this.diff_commonPrefix(text1,text2);var commonprefix=text1.substring(0,commonlength);text1=text1.substring(commonlength);text2=text2.substring(commonlength);commonlength=this.diff_commonSuffix(text1,text2);var commonsuffix=text1.substring(text1.length-commonlength);text1=text1.substring(0,text1.length-commonlength);text2=text2.substring(0,text2.length-commonlength);var diffs=this.diff_compute_(text1,text2,checklines,deadline);if(commonprefix){diffs.unshift([DIFF_EQUAL,commonprefix]);}if(commonsuffix){diffs.push([DIFF_EQUAL,commonsuffix]);}this.diff_cleanupMerge(diffs);return diffs;};diff_match_patch.prototype.diff_compute_=function(text1,text2,checklines,deadline){var diffs;if(!text1){return[[DIFF_INSERT,text2]];}if(!text2){return[[DIFF_DELETE,text1]];}var longtext=text1.length\u003etext2.length?text1:text2;var shorttext=text1.length\u003etext2.length?text2:text1;var i=longtext.indexOf(shorttext);if(i!=-1){diffs=[[DIFF_INSERT,longtext.substring(0,i)],[DIFF_EQUAL,shorttext],[DIFF_INSERT,longtext.substring(i+shorttext.length)]];if(text1.length\u003etext2.length){diffs[0][0]=diffs[2][0]=DIFF_DELETE;}return diffs;}if(shorttext.length==1){return[[DIFF_DELETE,text1],[DIFF_INSERT,text2]];}var hm=this.diff_halfMatch_(text1,text2);if(hm){var text1_a=hm[0];var text1_b=hm[1];var text2_a=hm[2];var text2_b=hm[3];var mid_common=hm[4];var diffs_a=this.diff_main(text1_a,text2_a,checklines,deadline);var diffs_b=this.diff_main(text1_b,text2_b,checklines,deadline);return diffs_a.concat([[DIFF_EQUAL,mid_common]],diffs_b);}if(checklines\u0026\u0026text1.length\u003e100\u0026\u0026text2.length\u003e100){return this.diff_lineMode_(text1,text2,deadline);}return this.diff_bisect_(text1,text2,deadline);};diff_match_patch.prototype.diff_lineMode_=function(text1,text2,deadline){var a=this.diff_linesToChars_(text1,text2);text1=a.chars1;text2=a.chars2;var linearray=a.lineArray;var diffs=this.diff_main(text1,text2,false,deadline);this.diff_charsToLines_(diffs,linearray);this.diff_cleanupSemantic(diffs);diffs.push([DIFF_EQUAL,'']);var pointer=0;var count_delete=0;var count_insert=0;var text_delete='';var text_insert='';while(pointer\u003cdiffs.length){switch(diffs[pointer][0]){case DIFF_INSERT:count_insert++;text_insert+=diffs[pointer][1];break;case DIFF_DELETE:count_delete++;text_delete+=diffs[pointer][1];break;case DIFF_EQUAL:if(count_delete\u003e=1\u0026\u0026count_insert\u003e=1){diffs.splice(pointer-count_delete-count_insert,count_delete+count_insert);pointer=pointer-count_delete-count_insert;var a=this.diff_main(text_delete,text_insert,false,deadline);for(var j=a.length-1;j\u003e=0;j--){diffs.splice(pointer,0,a[j]);}pointer=pointer+a.length;}count_insert=0;count_delete=0;text_delete='';text_insert='';break;}pointer++;}diffs.pop();return diffs;};diff_match_patch.prototype.diff_bisect_=function(text1,text2,deadline){var text1_length=text1.length;var text2_length=text2.length;var max_d=Math.ceil((text1_length+text2_length)/2);var v_offset=max_d;var v_length=2*max_d;var v1=new Array(v_length);var v2=new Array(v_length);for(var x=0;x\u003cv_length;x++){v1[x]=-1;v2[x]=-1;}v1[v_offset+1]=0;v2[v_offset+1]=0;var delta=text1_length-text2_length;var front=(delta%2!=0);var k1start=0;var k1end=0;var k2start=0;var k2end=0;for(var d=0;d\u003cmax_d;d++){if((new Date()).getTime()\u003edeadline){break;}for(var k1=-d+k1start;k1\u003c=d-k1end;k1+=2){var k1_offset=v_offset+k1;var x1;if(k1==-d||(k1!=d\u0026\u0026v1[k1_offset-1]\u003cv1[k1_offset+1])){x1=v1[k1_offset+1];}else{x1=v1[k1_offset-1]+1;}var y1=x1-k1;while(x1\u003ctext1_length\u0026\u0026y1\u003ctext2_length\u0026\u0026text1.charAt(x1)==text2.charAt(y1)){x1++;y1++;}v1[k1_offset]=x1;if(x1\u003etext1_length){k1end+=2;}else if(y1\u003etext2_length){k1start+=2;}else if(front){var k2_offset=v_offset+delta-k1;if(k2_offset\u003e=0\u0026\u0026k2_offset\u003cv_length\u0026\u0026v2[k2_offset]!=-1){var x2=text1_length-v2[k2_offset];if(x1\u003e=x2){return this.diff_bisectSplit_(text1,text2,x1,y1,deadline);}}}}for(var k2=-d+k2start;k2\u003c=d-k2end;k2+=2){var k2_offset=v_offset+k2;var x2;if(k2==-d||(k2!=d\u0026\u0026v2[k2_offset-1]\u003cv2[k2_offset+1])){x2=v2[k2_offset+1];}else{x2=v2[k2_offset-1]+1;}var y2=x2-k2;while(x2\u003ctext1_length\u0026\u0026y2\u003ctext2_length\u0026\u0026text1.charAt(text1_length-x2-1)==text2.charAt(text2_length-y2-1)){x2++;y2++;}v2[k2_offset]=x2;if(x2\u003etext1_length){k2end+=2;}else if(y2\u003etext2_length){k2start+=2;}else if(!front){var k1_offset=v_offset+delta-k2;if(k1_offset\u003e=0\u0026\u0026k1_offset\u003cv_length\u0026\u0026v1[k1_offset]!=-1){var x1=v1[k1_offset];var y1=v_offset+x1-k1_offset;x2=text1_length-x2;if(x1\u003e=x2){return this.diff_bisectSplit_(text1,text2,x1,y1,deadline);}}}}}return[[DIFF_DELETE,text1],[DIFF_INSERT,text2]];};diff_match_patch.prototype.diff_bisectSplit_=function(text1,text2,x,y,deadline){var text1a=text1.substring(0,x);var text2a=text2.substring(0,y);var text1b=text1.substring(x);var text2b=text2.substring(y);var diffs=this.diff_main(text1a,text2a,false,deadline);var diffsb=this.diff_main(text1b,text2b,false,deadline);return diffs.concat(diffsb);};diff_match_patch.prototype.diff_linesToChars_=function(text1,text2){var lineArray=[];var lineHash={};lineArray[0]='';function diff_linesToCharsMunge_(text){var chars='';var lineStart=0;var lineEnd=-1;var lineArrayLength=lineArray.length;while(lineEnd\u003ctext.length-1){lineEnd=text.indexOf('\\n',lineStart);if(lineEnd==-1){lineEnd=text.length-1;}var line=text.substring(lineStart,lineEnd+1);lineStart=lineEnd+1;if(lineHash.hasOwnProperty?lineHash.hasOwnProperty(line):(lineHash[line]!==undefined)){chars+=String.fromCharCode(lineHash[line]);}else{chars+=String.fromCharCode(lineArrayLength);lineHash[line]=lineArrayLength;lineArray[lineArrayLength++]=line;}}return chars;}var chars1=diff_linesToCharsMunge_(text1);var chars2=diff_linesToCharsMunge_(text2);return{chars1:chars1,chars2:chars2,lineArray:lineArray};};diff_match_patch.prototype.diff_charsToLines_=function(diffs,lineArray){for(var x=0;x\u003cdiffs.length;x++){var chars=diffs[x][1];var text=[];for(var y=0;y\u003cchars.length;y++){text[y]=lineArray[chars.charCodeAt(y)];}diffs[x][1]=text.join('');}};diff_match_patch.prototype.diff_commonPrefix=function(text1,text2){if(!text1||!text2||text1.charAt(0)!=text2.charAt(0)){return 0;}var pointermin=0;var pointermax=Math.min(text1.length,text2.length);var pointermid=pointermax;var pointerstart=0;while(pointermin\u003cpointermid){if(text1.substring(pointerstart,pointermid)==text2.substring(pointerstart,pointermid)){pointermin=pointermid;pointerstart=pointermin;}else{pointermax=pointermid;}pointermid=Math.floor((pointermax-pointermin)/2+pointermin);}return pointermid;};diff_match_patch.prototype.diff_commonSuffix=function(text1,text2){if(!text1||!text2||text1.charAt(text1.length-1)!=text2.charAt(text2.length-1)){return 0;}var pointermin=0;var pointermax=Math.min(text1.length,text2.length);var pointermid=pointermax;var pointerend=0;while(pointermin\u003cpointermid){if(text1.substring(text1.length-pointermid,text1.length-pointerend)==text2.substring(text2.length-pointermid,text2.length-pointerend)){pointermin=pointermid;pointerend=pointermin;}else{pointermax=pointermid;}pointermid=Math.floor((pointermax-pointermin)/2+pointermin);}return pointermid;};diff_match_patch.prototype.diff_commonOverlap_=function(text1,text2){var text1_length=text1.length;var text2_length=text2.length;if(text1_length==0||text2_length==0){return 0;}if(text1_length\u003etext2_length){text1=text1.substring(text1_length-text2_length);}else if(text1_length\u003ctext2_length){text2=text2.substring(0,text1_length);}var text_length=Math.min(text1_length,text2_length);if(text1==text2){return text_length;}var best=0;var length=1;while(true){var pattern=text1.substring(text_length-length);var found=text2.indexOf(pattern);if(found==-1){return best;}length+=found;if(found==0||text1.substring(text_length-length)==text2.substring(0,length)){best=length;length++;}}};diff_match_patch.prototype.diff_halfMatch_=function(text1,text2){if(this.Diff_Timeout\u003c=0){return null;}var longtext=text1.length\u003etext2.length?text1:text2;var shorttext=text1.length\u003etext2.length?text2:text1;if(longtext.length\u003c4||shorttext.length*2\u003clongtext.length){return null;}var dmp=this;function diff_halfMatchI_(longtext,shorttext,i){var seed=longtext.substring(i,i+Math.floor(longtext.length/4));var j=-1;var best_common='';var best_longtext_a,best_longtext_b,best_shorttext_a,best_shorttext_b;while((j=shorttext.indexOf(seed,j+1))!=-1){var prefixLength=dmp.diff_commonPrefix(longtext.substring(i),shorttext.substring(j));var suffixLength=dmp.diff_commonSuffix(longtext.substring(0,i),shorttext.substring(0,j));if(best_common.length\u003csuffixLength+prefixLength){best_common=shorttext.substring(j-suffixLength,j)+shorttext.substring(j,j+prefixLength);best_longtext_a=longtext.substring(0,i-suffixLength);best_longtext_b=longtext.substring(i+prefixLength);best_shorttext_a=shorttext.substring(0,j-suffixLength);best_shorttext_b=shorttext.substring(j+prefixLength);}}if(best_common.length*2\u003e=longtext.length){return[best_longtext_a,best_longtext_b,best_shorttext_a,best_shorttext_b,best_common];}else{return null;}}var hm1=diff_halfMatchI_(longtext,shorttext,Math.ceil(longtext.length/4));var hm2=diff_halfMatchI_(longtext,shorttext,Math.ceil(longtext.length/2));var hm;if(!hm1\u0026\u0026!hm2){return null;}else if(!hm2){hm=hm1;}else if(!hm1){hm=hm2;}else{hm=hm1[4].length\u003ehm2[4].length?hm1:hm2;}var text1_a,text1_b,text2_a,text2_b;if(text1.length\u003etext2.length){text1_a=hm[0];text1_b=hm[1];text2_a=hm[2];text2_b=hm[3];}else{text2_a=hm[0];text2_b=hm[1];text1_a=hm[2];text1_b=hm[3];}var mid_common=hm[4];return[text1_a,text1_b,text2_a,text2_b,mid_common];};diff_match_patch.prototype.diff_cleanupSemantic=function(diffs){var changes=false;var equalities=[];var equalitiesLength=0;var lastequality=null;var pointer=0;var length_insertions1=0;var length_deletions1=0;var length_insertions2=0;var length_deletions2=0;while(pointer\u003cdiffs.length){if(diffs[pointer][0]==DIFF_EQUAL){equalities[equalitiesLength++]=pointer;length_insertions1=length_insertions2;length_deletions1=length_deletions2;length_insertions2=0;length_deletions2=0;lastequality=diffs[pointer][1];}else{if(diffs[pointer][0]==DIFF_INSERT){length_insertions2+=diffs[pointer][1].length;}else{length_deletions2+=diffs[pointer][1].length;}if(lastequality\u0026\u0026(lastequality.length\u003c=Math.max(length_insertions1,length_deletions1))\u0026\u0026(lastequality.length\u003c=Math.max(length_insertions2,length_deletions2))){diffs.splice(equalities[equalitiesLength-1],0,[DIFF_DELETE,lastequality]);diffs[equalities[equalitiesLength-1]+1][0]=DIFF_INSERT;equalitiesLength--;equalitiesLength--;pointer=equalitiesLength\u003e0?equalities[equalitiesLength-1]:-1;length_insertions1=0;length_deletions1=0;length_insertions2=0;length_deletions2=0;lastequality=null;changes=true;}}pointer++;}if(changes){this.diff_cleanupMerge(diffs);}this.diff_cleanupSemanticLossless(diffs);pointer=1;while(pointer\u003cdiffs.length){if(diffs[pointer-1][0]==DIFF_DELETE\u0026\u0026diffs[pointer][0]==DIFF_INSERT){var deletion=diffs[pointer-1][1];var insertion=diffs[pointer][1];var overlap_length1=this.diff_commonOverlap_(deletion,insertion);var overlap_length2=this.diff_commonOverlap_(insertion,deletion);if(overlap_length1\u003e=overlap_length2){if(overlap_length1\u003e=deletion.length/2||overlap_length1\u003e=insertion.length/2){diffs.splice(pointer,0,[DIFF_EQUAL,insertion.substring(0,overlap_length1)]);diffs[pointer-1][1]=deletion.substring(0,deletion.length-overlap_length1);diffs[pointer+1][1]=insertion.substring(overlap_length1);pointer++;}}else{if(overlap_length2\u003e=deletion.length/2||overlap_length2\u003e=insertion.length/2){diffs.splice(pointer,0,[DIFF_EQUAL,deletion.substring(0,overlap_length2)]);diffs[pointer-1][0]=DIFF_INSERT;diffs[pointer-1][1]=insertion.substring(0,insertion.length-overlap_length2);diffs[pointer+1][0]=DIFF_DELETE;diffs[pointer+1][1]=deletion.substring(overlap_length2);pointer++;}}pointer++;}pointer++;}};diff_match_patch.prototype.diff_cleanupSemanticLossless=function(diffs){function diff_cleanupSemanticScore_(one,two){if(!one||!two){return 6;}var char1=one.charAt(one.length-1);var char2=two.charAt(0);var nonAlphaNumeric1=char1.match(diff_match_patch.nonAlphaNumericRegex_);var nonAlphaNumeric2=char2.match(diff_match_patch.nonAlphaNumericRegex_);var whitespace1=nonAlphaNumeric1\u0026\u0026char1.match(diff_match_patch.whitespaceRegex_);var whitespace2=nonAlphaNumeric2\u0026\u0026char2.match(diff_match_patch.whitespaceRegex_);var lineBreak1=whitespace1\u0026\u0026char1.match(diff_match_patch.linebreakRegex_);var lineBreak2=whitespace2\u0026\u0026char2.match(diff_match_patch.linebreakRegex_);var blankLine1=lineBreak1\u0026\u0026one.match(diff_match_patch.blanklineEndRegex_);var blankLine2=lineBreak2\u0026\u0026two.match(diff_match_patch.blanklineStartRegex_);if(blankLine1||blankLine2){return 5;}else if(lineBreak1||lineBreak2){return 4;}else if(nonAlphaNumeric1\u0026\u0026!whitespace1\u0026\u0026whitespace2){return 3;}else if(whitespace1||whitespace2){return 2;}else if(nonAlphaNumeric1||nonAlphaNumeric2){return 1;}return 0;}var pointer=1;while(pointer\u003cdiffs.length-1){if(diffs[pointer-1][0]==DIFF_EQUAL\u0026\u0026diffs[pointer+1][0]==DIFF_EQUAL){var equality1=diffs[pointer-1][1];var edit=diffs[pointer][1];var equality2=diffs[pointer+1][1];var commonOffset=this.diff_commonSuffix(equality1,edit);if(commonOffset){var commonString=edit.substring(edit.length-commonOffset);equality1=equality1.substring(0,equality1.length-commonOffset);edit=commonString+edit.substring(0,edit.length-commonOffset);equality2=commonString+equality2;}var bestEquality1=equality1;var bestEdit=edit;var bestEquality2=equality2;var bestScore=diff_cleanupSemanticScore_(equality1,edit)+diff_cleanupSemanticScore_(edit,equality2);while(edit.charAt(0)===equality2.charAt(0)){equality1+=edit.charAt(0);edit=edit.substring(1)+equality2.charAt(0);equality2=equality2.substring(1);var score=diff_cleanupSemanticScore_(equality1,edit)+diff_cleanupSemanticScore_(edit,equality2);if(score\u003e=bestScore){bestScore=score;bestEquality1=equality1;bestEdit=edit;bestEquality2=equality2;}}if(diffs[pointer-1][1]!=bestEquality1){if(bestEquality1){diffs[pointer-1][1]=bestEquality1;}else{diffs.splice(pointer-1,1);pointer--;}diffs[pointer][1]=bestEdit;if(bestEquality2){diffs[pointer+1][1]=bestEquality2;}else{diffs.splice(pointer+1,1);pointer--;}}}pointer++;}};diff_match_patch.nonAlphaNumericRegex_=/[^a-zA-Z0-9]/;diff_match_patch.whitespaceRegex_=/\\s/;diff_match_patch.linebreakRegex_=/[\\r\\n]/;diff_match_patch.blanklineEndRegex_=/\\n\\r?\\n$/;diff_match_patch.blanklineStartRegex_=/^\\r?\\n\\r?\\n/;diff_match_patch.prototype.diff_cleanupEfficiency=function(diffs){var changes=false;var equalities=[];var equalitiesLength=0;var lastequality=null;var pointer=0;var pre_ins=false;var pre_del=false;var post_ins=false;var post_del=false;while(pointer\u003cdiffs.length){if(diffs[pointer][0]==DIFF_EQUAL){if(diffs[pointer][1].length\u003cthis.Diff_EditCost\u0026\u0026(post_ins||post_del)){equalities[equalitiesLength++]=pointer;pre_ins=post_ins;pre_del=post_del;lastequality=diffs[pointer][1];}else{equalitiesLength=0;lastequality=null;}post_ins=post_del=false;}else{if(diffs[pointer][0]==DIFF_DELETE){post_del=true;}else{post_ins=true;}if(lastequality\u0026\u0026((pre_ins\u0026\u0026pre_del\u0026\u0026post_ins\u0026\u0026post_del)||((lastequality.length\u003cthis.Diff_EditCost/2)\u0026\u0026(pre_ins+pre_del+post_ins+post_del)==3))){diffs.splice(equalities[equalitiesLength-1],0,[DIFF_DELETE,lastequality]);diffs[equalities[equalitiesLength-1]+1][0]=DIFF_INSERT;equalitiesLength--;lastequality=null;if(pre_ins\u0026\u0026pre_del){post_ins=post_del=true;equalitiesLength=0;}else{equalitiesLength--;pointer=equalitiesLength\u003e0?equalities[equalitiesLength-1]:-1;post_ins=post_del=false;}changes=true;}}pointer++;}if(changes){this.diff_cleanupMerge(diffs);}};diff_match_patch.prototype.diff_cleanupMerge=function(diffs){diffs.push([DIFF_EQUAL,'']);var pointer=0;var count_delete=0;var count_insert=0;var text_delete='';var text_insert='';var commonlength;while(pointer\u003cdiffs.length){switch(diffs[pointer][0]){case DIFF_INSERT:count_insert++;text_insert+=diffs[pointer][1];pointer++;break;case DIFF_DELETE:count_delete++;text_delete+=diffs[pointer][1];pointer++;break;case DIFF_EQUAL:if(count_delete+count_insert\u003e1){if(count_delete!==0\u0026\u0026count_insert!==0){commonlength=this.diff_commonPrefix(text_insert,text_delete);if(commonlength!==0){if((pointer-count_delete-count_insert)\u003e0\u0026\u0026diffs[pointer-count_delete-count_insert-1][0]==DIFF_EQUAL){diffs[pointer-count_delete-count_insert-1][1]+=text_insert.substring(0,commonlength);}else{diffs.splice(0,0,[DIFF_EQUAL,text_insert.substring(0,commonlength)]);pointer++;}text_insert=text_insert.substring(commonlength);text_delete=text_delete.substring(commonlength);}commonlength=this.diff_commonSuffix(text_insert,text_delete);if(commonlength!==0){diffs[pointer][1]=text_insert.substring(text_insert.length-commonlength)+diffs[pointer][1];text_insert=text_insert.substring(0,text_insert.length-commonlength);text_delete=text_delete.substring(0,text_delete.length-commonlength);}}if(count_delete===0){diffs.splice(pointer-count_insert,count_delete+count_insert,[DIFF_INSERT,text_insert]);}else if(count_insert===0){diffs.splice(pointer-count_delete,count_delete+count_insert,[DIFF_DELETE,text_delete]);}else{diffs.splice(pointer-count_delete-count_insert,count_delete+count_insert,[DIFF_DELETE,text_delete],[DIFF_INSERT,text_insert]);}pointer=pointer-count_delete-count_insert+(count_delete?1:0)+(count_insert?1:0)+1;}else if(pointer!==0\u0026\u0026diffs[pointer-1][0]==DIFF_EQUAL){diffs[pointer-1][1]+=diffs[pointer][1];diffs.splice(pointer,1);}else{pointer++;}count_insert=0;count_delete=0;text_delete='';text_insert='';break;}}if(diffs[diffs.length-1][1]===''){diffs.pop();}var changes=false;pointer=1;while(pointer\u003cdiffs.length-1){if(diffs[pointer-1][0]==DIFF_EQUAL\u0026\u0026diffs[pointer+1][0]==DIFF_EQUAL){if(diffs[pointer][1].substring(diffs[pointer][1].length-diffs[pointer-1][1].length)==diffs[pointer-1][1]){diffs[pointer][1]=diffs[pointer-1][1]+diffs[pointer][1].substring(0,diffs[pointer][1].length-diffs[pointer-1][1].length);diffs[pointer+1][1]=diffs[pointer-1][1]+diffs[pointer+1][1];diffs.splice(pointer-1,1);changes=true;}else if(diffs[pointer][1].substring(0,diffs[pointer+1][1].length)==diffs[pointer+1][1]){diffs[pointer-1][1]+=diffs[pointer+1][1];diffs[pointer][1]=diffs[pointer][1].substring(diffs[pointer+1][1].length)+diffs[pointer+1][1];diffs.splice(pointer+1,1);changes=true;}}pointer++;}if(changes){this.diff_cleanupMerge(diffs);}};diff_match_patch.prototype.diff_xIndex=function(diffs,loc){var chars1=0;var chars2=0;var last_chars1=0;var last_chars2=0;var x;for(x=0;x\u003cdiffs.length;x++){if(diffs[x][0]!==DIFF_INSERT){chars1+=diffs[x][1].length;}if(diffs[x][0]!==DIFF_DELETE){chars2+=diffs[x][1].length;}if(chars1\u003eloc){break;}last_chars1=chars1;last_chars2=chars2;}if(diffs.length!=x\u0026\u0026diffs[x][0]===DIFF_DELETE){return last_chars2;}return last_chars2+(loc-last_chars1);};diff_match_patch.prototype.diff_prettyHtml=function(diffs){var html=[];var pattern_amp=/\u0026/g;var pattern_lt=/\u003c/g;var pattern_gt=/\u003e/g;var pattern_para=/\\n/g;for(var x=0;x\u003cdiffs.length;x++){var op=diffs[x][0];var data=diffs[x][1];var text=data.replace(pattern_amp,'\u0026amp;').replace(pattern_lt,'\u0026lt;').replace(pattern_gt,'\u0026gt;').replace(pattern_para,'\u0026para;\u003cbr\u003e');switch(op){case DIFF_INSERT:html[x]='\u003cins style=\"background:#e6ffe6;\"\u003e'+text+'\u003c/ins\u003e';break;case DIFF_DELETE:html[x]='\u003cdel style=\"background:#ffe6e6;\"\u003e'+text+'\u003c/del\u003e';break;case DIFF_EQUAL:html[x]='\u003cspan\u003e'+text+'\u003c/span\u003e';break;}}return html.join('');};diff_match_patch.prototype.diff_text1=function(diffs){var text=[];for(var x=0;x\u003cdiffs.length;x++){if(diffs[x][0]!==DIFF_INSERT){text[x]=diffs[x][1];}}return text.join('');};diff_match_patch.prototype.diff_text2=function(diffs){var text=[];for(var x=0;x\u003cdiffs.length;x++){if(diffs[x][0]!==DIFF_DELETE){text[x]=diffs[x][1];}}return text.join('');};diff_match_patch.prototype.diff_levenshtein=function(diffs){var levenshtein=0;var insertions=0;var deletions=0;for(var x=0;x\u003cdiffs.length;x++){var op=diffs[x][0];var data=diffs[x][1];switch(op){case DIFF_INSERT:insertions+=data.length;break;case DIFF_DELETE:deletions+=data.length;break;case DIFF_EQUAL:levenshtein+=Math.max(insertions,deletions);insertions=0;deletions=0;break;}}levenshtein+=Math.max(insertions,deletions);return levenshtein;};diff_match_patch.prototype.diff_toDelta=function(diffs){var text=[];for(var x=0;x\u003cdiffs.length;x++){switch(diffs[x][0]){case DIFF_INSERT:text[x]='+'+encodeURI(diffs[x][1]);break;case DIFF_DELETE:text[x]='-'+diffs[x][1].length;break;case DIFF_EQUAL:text[x]='='+diffs[x][1].length;break;}}return text.join('\\t').replace(/%20/g,' ');};diff_match_patch.prototype.diff_fromDelta=function(text1,delta){var diffs=[];var diffsLength=0;var pointer=0;var tokens=delta.split(/\\t/g);for(var x=0;x\u003ctokens.length;x++){var param=tokens[x].substring(1);switch(tokens[x].charAt(0)){case'+':try{diffs[diffsLength++]=[DIFF_INSERT,decodeURI(param)];}catch(ex){throw new Error('Illegal escape in diff_fromDelta: '+param);}break;case'-':case'=':var n=parseInt(param,10);if(isNaN(n)||n\u003c0){throw new Error('Invalid number in diff_fromDelta: '+param);}var text=text1.substring(pointer,pointer+=n);if(tokens[x].charAt(0)=='='){diffs[diffsLength++]=[DIFF_EQUAL,text];}else{diffs[diffsLength++]=[DIFF_DELETE,text];}break;default:if(tokens[x]){throw new Error('Invalid diff operation in diff_fromDelta: '+tokens[x]);}}}if(pointer!=text1.length){throw new Error('Delta length ('+pointer+') does not equal source text length ('+text1.length+').');}return diffs;};diff_match_patch.prototype.match_main=function(text,pattern,loc){if(text==null||pattern==null||loc==null){throw new Error('Null input. (match_main)');}loc=Math.max(0,Math.min(loc,text.length));if(text==pattern){return 0;}else if(!text.length){return-1;}else if(text.substring(loc,loc+pattern.length)==pattern){return loc;}else{return this.match_bitap_(text,pattern,loc);}};diff_match_patch.prototype.match_bitap_=function(text,pattern,loc){if(pattern.length\u003ethis.Match_MaxBits){throw new Error('Pattern too long for this browser.');}var s=this.match_alphabet_(pattern);var dmp=this;function match_bitapScore_(e,x){var accuracy=e/pattern.length;var proximity=Math.abs(loc-x);if(!dmp.Match_Distance){return proximity?1.0:accuracy;}return accuracy+(proximity/dmp.Match_Distance);}var score_threshold=this.Match_Threshold;var best_loc=text.indexOf(pattern,loc);if(best_loc!=-1){score_threshold=Math.min(match_bitapScore_(0,best_loc),score_threshold);best_loc=text.lastIndexOf(pattern,loc+pattern.length);if(best_loc!=-1){score_threshold=Math.min(match_bitapScore_(0,best_loc),score_threshold);}}var matchmask=1\u003c\u003c(pattern.length-1);best_loc=-1;var bin_min,bin_mid;var bin_max=pattern.length+text.length;var last_rd;for(var d=0;d\u003cpattern.length;d++){bin_min=0;bin_mid=bin_max;while(bin_min\u003cbin_mid){if(match_bitapScore_(d,loc+bin_mid)\u003c=score_threshold){bin_min=bin_mid;}else{bin_max=bin_mid;}bin_mid=Math.floor((bin_max-bin_min)/2+bin_min);}bin_max=bin_mid;var start=Math.max(1,loc-bin_mid+1);var finish=Math.min(loc+bin_mid,text.length)+pattern.length;var rd=Array(finish+2);rd[finish+1]=(1\u003c\u003cd)-1;for(var j=finish;j\u003e=start;j--){var charMatch=s[text.charAt(j-1)];if(d===0){rd[j]=((rd[j+1]\u003c\u003c1)|1)\u0026charMatch;}else{rd[j]=(((rd[j+1]\u003c\u003c1)|1)\u0026charMatch)|(((last_rd[j+1]|last_rd[j])\u003c\u003c1)|1)|last_rd[j+1];}if(rd[j]\u0026matchmask){var score=match_bitapScore_(d,j-1);if(score\u003c=score_threshold){score_threshold=score;best_loc=j-1;if(best_loc\u003eloc){start=Math.max(1,2*loc-best_loc);}else{break;}}}}if(match_bitapScore_(d+1,loc)\u003escore_threshold){break;}last_rd=rd;}return best_loc;};diff_match_patch.prototype.match_alphabet_=function(pattern){var s={};for(var i=0;i\u003cpattern.length;i++){s[pattern.charAt(i)]=0;}for(var i=0;i\u003cpattern.length;i++){s[pattern.charAt(i)]|=1\u003c\u003c(pattern.length-i-1);}return s;};diff_match_patch.prototype.patch_addContext_=function(patch,text){if(text.length==0){return;}var pattern=text.substring(patch.start2,patch.start2+patch.length1);var padding=0;while(text.indexOf(pattern)!=text.lastIndexOf(pattern)\u0026\u0026pattern.length\u003cthis.Match_MaxBits-this.Patch_Margin-this.Patch_Margin){padding+=this.Patch_Margin;pattern=text.substring(patch.start2-padding,patch.start2+patch.length1+padding);}padding+=this.Patch_Margin;var prefix=text.substring(patch.start2-padding,patch.start2);if(prefix){patch.diffs.unshift([DIFF_EQUAL,prefix]);}var suffix=text.substring(patch.start2+patch.length1,patch.start2+patch.length1+padding);if(suffix){patch.diffs.push([DIFF_EQUAL,suffix]);}patch.start1-=prefix.length;patch.start2-=prefix.length;patch.length1+=prefix.length+suffix.length;patch.length2+=prefix.length+suffix.length;};diff_match_patch.prototype.patch_make=function(a,opt_b,opt_c){var text1,diffs;if(typeof a=='string'\u0026\u0026typeof opt_b=='string'\u0026\u0026typeof opt_c=='undefined'){text1=(a);diffs=this.diff_main(text1,(opt_b),true);if(diffs.length\u003e2){this.diff_cleanupSemantic(diffs);this.diff_cleanupEfficiency(diffs);}}else if(a\u0026\u0026typeof a=='object'\u0026\u0026typeof opt_b=='undefined'\u0026\u0026typeof opt_c=='undefined'){diffs=(a);text1=this.diff_text1(diffs);}else if(typeof a=='string'\u0026\u0026opt_b\u0026\u0026typeof opt_b=='object'\u0026\u0026typeof opt_c=='undefined'){text1=(a);diffs=(opt_b);}else if(typeof a=='string'\u0026\u0026typeof opt_b=='string'\u0026\u0026opt_c\u0026\u0026typeof opt_c=='object'){text1=(a);diffs=(opt_c);}else{throw new Error('Unknown call format to patch_make.');}if(diffs.length===0){return[];}var patches=[];var patch=new diff_match_patch.patch_obj();var patchDiffLength=0;var char_count1=0;var char_count2=0;var prepatch_text=text1;var postpatch_text=text1;for(var x=0;x\u003cdiffs.length;x++){var diff_type=diffs[x][0];var diff_text=diffs[x][1];if(!patchDiffLength\u0026\u0026diff_type!==DIFF_EQUAL){patch.start1=char_count1;patch.start2=char_count2;}switch(diff_type){case DIFF_INSERT:patch.diffs[patchDiffLength++]=diffs[x];patch.length2+=diff_text.length;postpatch_text=postpatch_text.substring(0,char_count2)+diff_text+postpatch_text.substring(char_count2);break;case DIFF_DELETE:patch.length1+=diff_text.length;patch.diffs[patchDiffLength++]=diffs[x];postpatch_text=postpatch_text.substring(0,char_count2)+postpatch_text.substring(char_count2+diff_text.length);break;case DIFF_EQUAL:if(diff_text.length\u003c=2*this.Patch_Margin\u0026\u0026patchDiffLength\u0026\u0026diffs.length!=x+1){patch.diffs[patchDiffLength++]=diffs[x];patch.length1+=diff_text.length;patch.length2+=diff_text.length;}else if(diff_text.length\u003e=2*this.Patch_Margin){if(patchDiffLength){this.patch_addContext_(patch,prepatch_text);patches.push(patch);patch=new diff_match_patch.patch_obj();patchDiffLength=0;prepatch_text=postpatch_text;char_count1=char_count2;}}break;}if(diff_type!==DIFF_INSERT){char_count1+=diff_text.length;}if(diff_type!==DIFF_DELETE){char_count2+=diff_text.length;}}if(patchDiffLength){this.patch_addContext_(patch,prepatch_text);patches.push(patch);}return patches;};diff_match_patch.prototype.patch_deepCopy=function(patches){var patchesCopy=[];for(var x=0;x\u003cpatches.length;x++){var patch=patches[x];var patchCopy=new diff_match_patch.patch_obj();patchCopy.diffs=[];for(var y=0;y\u003cpatch.diffs.length;y++){patchCopy.diffs[y]=patch.diffs[y].slice();}patchCopy.start1=patch.start1;patchCopy.start2=patch.start2;patchCopy.length1=patch.length1;patchCopy.length2=patch.length2;patchesCopy[x]=patchCopy;}return patchesCopy;};diff_match_patch.prototype.patch_apply=function(patches,text){if(patches.length==0){return[text,[]];}patches=this.patch_deepCopy(patches);var nullPadding=this.patch_addPadding(patches);text=nullPadding+text+nullPadding;this.patch_splitMax(patches);var delta=0;var results=[];for(var x=0;x\u003cpatches.length;x++){var expected_loc=patches[x].start2+delta;var text1=this.diff_text1(patches[x].diffs);var start_loc;var end_loc=-1;if(text1.length\u003ethis.Match_MaxBits){start_loc=this.match_main(text,text1.substring(0,this.Match_MaxBits),expected_loc);if(start_loc!=-1){end_loc=this.match_main(text,text1.substring(text1.length-this.Match_MaxBits),expected_loc+text1.length-this.Match_MaxBits);if(end_loc==-1||start_loc\u003e=end_loc){start_loc=-1;}}}else{start_loc=this.match_main(text,text1,expected_loc);}if(start_loc==-1){results[x]=false;delta-=patches[x].length2-patches[x].length1;}else{results[x]=true;delta=start_loc-expected_loc;var text2;if(end_loc==-1){text2=text.substring(start_loc,start_loc+text1.length);}else{text2=text.substring(start_loc,end_loc+this.Match_MaxBits);}if(text1==text2){text=text.substring(0,start_loc)+this.diff_text2(patches[x].diffs)+text.substring(start_loc+text1.length);}else{var diffs=this.diff_main(text1,text2,false);if(text1.length\u003ethis.Match_MaxBits\u0026\u0026this.diff_levenshtein(diffs)/text1.length\u003ethis.Patch_DeleteThreshold){results[x]=false;}else{this.diff_cleanupSemanticLossless(diffs);var index1=0;var index2;for(var y=0;y\u003cpatches[x].diffs.length;y++){var mod=patches[x].diffs[y];if(mod[0]!==DIFF_EQUAL){index2=this.diff_xIndex(diffs,index1);}if(mod[0]===DIFF_INSERT){text=text.substring(0,start_loc+index2)+mod[1]+text.substring(start_loc+index2);}else if(mod[0]===DIFF_DELETE){text=text.substring(0,start_loc+index2)+text.substring(start_loc+this.diff_xIndex(diffs,index1+mod[1].length));}if(mod[0]!==DIFF_DELETE){index1+=mod[1].length;}}}}}}text=text.substring(nullPadding.length,text.length-nullPadding.length);return[text,results];};diff_match_patch.prototype.patch_addPadding=function(patches){var paddingLength=this.Patch_Margin;var nullPadding='';for(var x=1;x\u003c=paddingLength;x++){nullPadding+=String.fromCharCode(x);}for(var x=0;x\u003cpatches.length;x++){patches[x].start1+=paddingLength;patches[x].start2+=paddingLength;}var patch=patches[0];var diffs=patch.diffs;if(diffs.length==0||diffs[0][0]!=DIFF_EQUAL){diffs.unshift([DIFF_EQUAL,nullPadding]);patch.start1-=paddingLength;patch.start2-=paddingLength;patch.length1+=paddingLength;patch.length2+=paddingLength;}else if(paddingLength\u003ediffs[0][1].length){var extraLength=paddingLength-diffs[0][1].length;diffs[0][1]=nullPadding.substring(diffs[0][1].length)+diffs[0][1];patch.start1-=extraLength;patch.start2-=extraLength;patch.length1+=extraLength;patch.length2+=extraLength;}patch=patches[patches.length-1];diffs=patch.diffs;if(diffs.length==0||diffs[diffs.length-1][0]!=DIFF_EQUAL){diffs.push([DIFF_EQUAL,nullPadding]);patch.length1+=paddingLength;patch.length2+=paddingLength;}else if(paddingLength\u003ediffs[diffs.length-1][1].length){var extraLength=paddingLength-diffs[diffs.length-1][1].length;diffs[diffs.length-1][1]+=nullPadding.substring(0,extraLength);patch.length1+=extraLength;patch.length2+=extraLength;}return nullPadding;};diff_match_patch.prototype.patch_splitMax=function(patches){var patch_size=this.Match_MaxBits;for(var x=0;x\u003cpatches.length;x++){if(patches[x].length1\u003c=patch_size){continue;}var bigpatch=patches[x];patches.splice(x--,1);var start1=bigpatch.start1;var start2=bigpatch.start2;var precontext='';while(bigpatch.diffs.length!==0){var patch=new diff_match_patch.patch_obj();var empty=true;patch.start1=start1-precontext.length;patch.start2=start2-precontext.length;if(precontext!==''){patch.length1=patch.length2=precontext.length;patch.diffs.push([DIFF_EQUAL,precontext]);}while(bigpatch.diffs.length!==0\u0026\u0026patch.length1\u003cpatch_size-this.Patch_Margin){var diff_type=bigpatch.diffs[0][0];var diff_text=bigpatch.diffs[0][1];if(diff_type===DIFF_INSERT){patch.length2+=diff_text.length;start2+=diff_text.length;patch.diffs.push(bigpatch.diffs.shift());empty=false;}else if(diff_type===DIFF_DELETE\u0026\u0026patch.diffs.length==1\u0026\u0026patch.diffs[0][0]==DIFF_EQUAL\u0026\u0026diff_text.length\u003e2*patch_size){patch.length1+=diff_text.length;start1+=diff_text.length;empty=false;patch.diffs.push([diff_type,diff_text]);bigpatch.diffs.shift();}else{diff_text=diff_text.substring(0,patch_size-patch.length1-this.Patch_Margin);patch.length1+=diff_text.length;start1+=diff_text.length;if(diff_type===DIFF_EQUAL){patch.length2+=diff_text.length;start2+=diff_text.length;}else{empty=false;}patch.diffs.push([diff_type,diff_text]);if(diff_text==bigpatch.diffs[0][1]){bigpatch.diffs.shift();}else{bigpatch.diffs[0][1]=bigpatch.diffs[0][1].substring(diff_text.length);}}}precontext=this.diff_text2(patch.diffs);precontext=precontext.substring(precontext.length-this.Patch_Margin);var postcontext=this.diff_text1(bigpatch.diffs).substring(0,this.Patch_Margin);if(postcontext!==''){patch.length1+=postcontext.length;patch.length2+=postcontext.length;if(patch.diffs.length!==0\u0026\u0026patch.diffs[patch.diffs.length-1][0]===DIFF_EQUAL){patch.diffs[patch.diffs.length-1][1]+=postcontext;}else{patch.diffs.push([DIFF_EQUAL,postcontext]);}}if(!empty){patches.splice(++x,0,patch);}}}};diff_match_patch.prototype.patch_toText=function(patches){var text=[];for(var x=0;x\u003cpatches.length;x++){text[x]=patches[x];}return text.join('');};diff_match_patch.prototype.patch_fromText=function(textline){var patches=[];if(!textline){return patches;}var text=textline.split('\\n');var textPointer=0;var patchHeader=/^@@ -(\\d+),?(\\d*) \\+(\\d+),?(\\d*) @@$/;while(textPointer\u003ctext.length){var m=text[textPointer].match(patchHeader);if(!m){throw new Error('Invalid patch string: '+text[textPointer]);}var patch=new diff_match_patch.patch_obj();patches.push(patch);patch.start1=parseInt(m[1],10);if(m[2]===''){patch.start1--;patch.length1=1;}else if(m[2]=='0'){patch.length1=0;}else{patch.start1--;patch.length1=parseInt(m[2],10);}patch.start2=parseInt(m[3],10);if(m[4]===''){patch.start2--;patch.length2=1;}else if(m[4]=='0'){patch.length2=0;}else{patch.start2--;patch.length2=parseInt(m[4],10);}textPointer++;while(textPointer\u003ctext.length){var sign=text[textPointer].charAt(0);try{var line=decodeURI(text[textPointer].substring(1));}catch(ex){throw new Error('Illegal escape in patch_fromText: '+line);}if(sign=='-'){patch.diffs.push([DIFF_DELETE,line]);}else if(sign=='+'){patch.diffs.push([DIFF_INSERT,line]);}else if(sign==' '){patch.diffs.push([DIFF_EQUAL,line]);}else if(sign=='@'){break;}else if(sign===''){}else{throw new Error('Invalid patch mode \"'+sign+'\" in: '+line);}textPointer++;}}return patches;};diff_match_patch.patch_obj=function(){this.diffs=[];this.start1=null;this.start2=null;this.length1=0;this.length2=0;};diff_match_patch.patch_obj.prototype.toString=function(){var coords1,coords2;if(this.length1===0){coords1=this.start1+',0';}else if(this.length1==1){coords1=this.start1+1;}else{coords1=(this.start1+1)+','+this.length1;}if(this.length2===0){coords2=this.start2+',0';}else if(this.length2==1){coords2=this.start2+1;}else{coords2=(this.start2+1)+','+this.length2;}var text=['@@ -'+coords1+' +'+coords2+' @@\\n'];var op;for(var x=0;x\u003cthis.diffs.length;x++){switch(this.diffs[x][0]){case DIFF_INSERT:op='+';break;case DIFF_DELETE:op='-';break;case DIFF_EQUAL:op=' ';break;}text[x+1]=op+encodeURI(this.diffs[x][1])+'\\n';}return text.join('').replace(/%20/g,' ');};module.exports=diff_match_patch;module.exports['diff_match_patch']=diff_match_patch;module.exports['DIFF_DELETE']=DIFF_DELETE;module.exports['DIFF_INSERT']=DIFF_INSERT;module.exports['DIFF_EQUAL']=DIFF_EQUAL;});var TEXT_DIFF=2;var DEFAULT_MIN_LENGTH=60;var cachedDiffPatch=null;var getDiffMatchPatch=function getDiffMatchPatch(required){if(!cachedDiffPatch){var instance=void 0;if(typeof diff_match_patch!=='undefined'){instance=typeof diff_match_patch==='function'?new diff_match_patch():new diff_match_patch.diff_match_patch();}else if(diffMatchPatch){try{instance=diffMatchPatch\u0026\u0026new diffMatchPatch();}catch(err){instance=null;}}if(!instance){if(!required){return null;}var error=new Error('text diff_match_patch library not found');error.diff_match_patch_not_found=true;throw error;}cachedDiffPatch={diff:function diff(txt1,txt2){return instance.patch_toText(instance.patch_make(txt1,txt2));},patch:function patch(txt1,_patch){var results=instance.patch_apply(instance.patch_fromText(_patch),txt1);for(var i=0;i\u003cresults[1].length;i++){if(!results[1][i]){var _error=new Error('text patch failed');_error.textPatchFailed=true;}}return results[0];}};}return cachedDiffPatch;};var diffFilter$3=function textsDiffFilter(context){if(context.leftType!=='string'){return;}var minLength=context.options\u0026\u0026context.options.textDiff\u0026\u0026context.options.textDiff.minLength||DEFAULT_MIN_LENGTH;if(context.left.length\u003cminLength||context.right.length\u003cminLength){context.setResult([context.left,context.right]).exit();return;}var diffMatchPatch$$1=getDiffMatchPatch();if(!diffMatchPatch$$1){context.setResult([context.left,context.right]).exit();return;}var diff=diffMatchPatch$$1.diff;context.setResult([diff(context.left,context.right),0,TEXT_DIFF]).exit();};diffFilter$3.filterName='texts';var patchFilter$3=function textsPatchFilter(context){if(context.nested){return;}if(context.delta[2]!==TEXT_DIFF){return;}var patch=getDiffMatchPatch(true).patch;context.setResult(patch(context.left,context.delta[0])).exit();};patchFilter$3.filterName='texts';var textDeltaReverse=function textDeltaReverse(delta){var i=void 0;var l=void 0;var lines=void 0;var line=void 0;var lineTmp=void 0;var header=null;var headerRegex=/^@@ +-(\\d+),(\\d+) +\\+(\\d+),(\\d+) +@@$/;var lineHeader=void 0;lines=delta.split('\\n');for(i=0,l=lines.length;i\u003cl;i++){line=lines[i];var lineStart=line.slice(0,1);if(lineStart==='@'){header=headerRegex.exec(line);lineHeader=i;lines[lineHeader]='@@ -'+header[3]+','+header[4]+' +'+header[1]+','+header[2]+' @@';}else if(lineStart==='+'){lines[i]='-'+lines[i].slice(1);if(lines[i-1].slice(0,1)==='+'){lineTmp=lines[i];lines[i]=lines[i-1];lines[i-1]=lineTmp;}}else if(lineStart==='-'){lines[i]='+'+lines[i].slice(1);}}return lines.join('\\n');};var reverseFilter$3=function textsReverseFilter(context){if(context.nested){return;}if(context.delta[2]!==TEXT_DIFF){return;}context.setResult([textDeltaReverse(context.delta[0]),0,TEXT_DIFF]).exit();};reverseFilter$3.filterName='texts';var DiffPatcher=function(){function DiffPatcher(options){classCallCheck(this,DiffPatcher);this.processor=new Processor(options);this.processor.pipe(new Pipe('diff').append(collectChildrenDiffFilter,diffFilter,diffFilter$2,diffFilter$3,objectsDiffFilter,diffFilter$1).shouldHaveResult());this.processor.pipe(new Pipe('patch').append(collectChildrenPatchFilter,collectChildrenPatchFilter$1,patchFilter,patchFilter$3,patchFilter$1,patchFilter$2).shouldHaveResult());this.processor.pipe(new Pipe('reverse').append(collectChildrenReverseFilter,collectChildrenReverseFilter$1,reverseFilter,reverseFilter$3,reverseFilter$1,reverseFilter$2).shouldHaveResult());}createClass(DiffPatcher,[{key:'options',value:function options(){var _processor;return(_processor=this.processor).options.apply(_processor,arguments);}},{key:'diff',value:function diff(left,right){return this.processor.process(new DiffContext(left,right));}},{key:'patch',value:function patch(left,delta){return this.processor.process(new PatchContext(left,delta));}},{key:'reverse',value:function reverse(delta){return this.processor.process(new ReverseContext(delta));}},{key:'unpatch',value:function unpatch(right,delta){return this.patch(right,this.reverse(delta));}},{key:'clone',value:function clone$$1(value){return clone(value);}}]);return DiffPatcher;}();var isArray$3=typeof Array.isArray==='function'?Array.isArray:function(a){return a instanceof Array;};var getObjectKeys=typeof Object.keys==='function'?function(obj){return Object.keys(obj);}:function(obj){var names=[];for(var property in obj){if(Object.prototype.hasOwnProperty.call(obj,property)){names.push(property);}}return names;};var trimUnderscore=function trimUnderscore(str){if(str.substr(0,1)==='_'){return str.slice(1);}return str;};var arrayKeyToSortNumber=function arrayKeyToSortNumber(key){if(key==='_t'){return-1;}else{if(key.substr(0,1)==='_'){return parseInt(key.slice(1),10);}else{return parseInt(key,10)+0.1;}}};var arrayKeyComparer=function arrayKeyComparer(key1,key2){return arrayKeyToSortNumber(key1)-arrayKeyToSortNumber(key2);};var BaseFormatter=function(){function BaseFormatter(){classCallCheck(this,BaseFormatter);}createClass(BaseFormatter,[{key:'format',value:function format(delta,left){var context={};this.prepareContext(context);this.recurse(context,delta,left);return this.finalize(context);}},{key:'prepareContext',value:function prepareContext(context){context.buffer=[];context.out=function(){var _buffer;(_buffer=this.buffer).push.apply(_buffer,arguments);};}},{key:'typeFormattterNotFound',value:function typeFormattterNotFound(context,deltaType){throw new Error('cannot format delta type: '+deltaType);}},{key:'typeFormattterErrorFormatter',value:function typeFormattterErrorFormatter(context,err){return err.toString();}},{key:'finalize',value:function finalize(_ref){var buffer=_ref.buffer;if(isArray$3(buffer)){return buffer.join('');}}},{key:'recurse',value:function recurse(context,delta,left,key,leftKey,movedFrom,isLast){var useMoveOriginHere=delta\u0026\u0026movedFrom;var leftValue=useMoveOriginHere?movedFrom.value:left;if(typeof delta==='undefined'\u0026\u0026typeof key==='undefined'){return undefined;}var type=this.getDeltaType(delta,movedFrom);var nodeType=type==='node'?delta._t==='a'?'array':'object':'';if(typeof key!=='undefined'){this.nodeBegin(context,key,leftKey,type,nodeType,isLast);}else{this.rootBegin(context,type,nodeType);}var typeFormattter=void 0;try{typeFormattter=this['format_'+type]||this.typeFormattterNotFound(context,type);typeFormattter.call(this,context,delta,leftValue,key,leftKey,movedFrom);}catch(err){this.typeFormattterErrorFormatter(context,err,delta,leftValue,key,leftKey,movedFrom);if(typeof console!=='undefined'\u0026\u0026console.error){console.error(err.stack);}}if(typeof key!=='undefined'){this.nodeEnd(context,key,leftKey,type,nodeType,isLast);}else{this.rootEnd(context,type,nodeType);}}},{key:'formatDeltaChildren',value:function formatDeltaChildren(context,delta,left){var self=this;this.forEachDeltaKey(delta,left,function(key,leftKey,movedFrom,isLast){self.recurse(context,delta[key],left?left[leftKey]:undefined,key,leftKey,movedFrom,isLast);});}},{key:'forEachDeltaKey',value:function forEachDeltaKey(delta,left,fn){var keys=getObjectKeys(delta);var arrayKeys=delta._t==='a';var moveDestinations={};var name=void 0;if(typeof left!=='undefined'){for(name in left){if(Object.prototype.hasOwnProperty.call(left,name)){if(typeof delta[name]==='undefined'\u0026\u0026(!arrayKeys||typeof delta['_'+name]==='undefined')){keys.push(name);}}}}for(name in delta){if(Object.prototype.hasOwnProperty.call(delta,name)){var value=delta[name];if(isArray$3(value)\u0026\u0026value[2]===3){moveDestinations[value[1].toString()]={key:name,value:left\u0026\u0026left[parseInt(name.substr(1))]};if(this.includeMoveDestinations!==false){if(typeof left==='undefined'\u0026\u0026typeof delta[value[1]]==='undefined'){keys.push(value[1].toString());}}}}}if(arrayKeys){keys.sort(arrayKeyComparer);}else{keys.sort();}for(var index=0,length=keys.length;index\u003clength;index++){var key=keys[index];if(arrayKeys\u0026\u0026key==='_t'){continue;}var leftKey=arrayKeys?typeof key==='number'?key:parseInt(trimUnderscore(key),10):key;var isLast=index===length-1;fn(key,leftKey,moveDestinations[leftKey],isLast);}}},{key:'getDeltaType',value:function getDeltaType(delta,movedFrom){if(typeof delta==='undefined'){if(typeof movedFrom!=='undefined'){return'movedestination';}return'unchanged';}if(isArray$3(delta)){if(delta.length===1){return'added';}if(delta.length===2){return'modified';}if(delta.length===3\u0026\u0026delta[2]===0){return'deleted';}if(delta.length===3\u0026\u0026delta[2]===2){return'textdiff';}if(delta.length===3\u0026\u0026delta[2]===3){return'moved';}}else if((typeof delta==='undefined'?'undefined':_typeof(delta))==='object'){return'node';}return'unknown';}},{key:'parseTextDiff',value:function parseTextDiff(value){var output=[];var lines=value.split('\\n@@ ');for(var i=0,l=lines.length;i\u003cl;i++){var line=lines[i];var lineOutput={pieces:[]};var location=/^(?:@@ )?[-+]?(\\d+),(\\d+)/.exec(line).slice(1);lineOutput.location={line:location[0],chr:location[1]};var pieces=line.split('\\n').slice(1);for(var pieceIndex=0,piecesLength=pieces.length;pieceIndex\u003cpiecesLength;pieceIndex++){var piece=pieces[pieceIndex];if(!piece.length){continue;}var pieceOutput={type:'context'};if(piece.substr(0,1)==='+'){pieceOutput.type='added';}else if(piece.substr(0,1)==='-'){pieceOutput.type='deleted';}pieceOutput.text=piece.slice(1);lineOutput.pieces.push(pieceOutput);}output.push(lineOutput);}return output;}}]);return BaseFormatter;}();var base=Object.freeze({default:BaseFormatter});var HtmlFormatter=function(_BaseFormatter){inherits(HtmlFormatter,_BaseFormatter);function HtmlFormatter(){classCallCheck(this,HtmlFormatter);return possibleConstructorReturn(this,(HtmlFormatter.__proto__||Object.getPrototypeOf(HtmlFormatter)).apply(this,arguments));}createClass(HtmlFormatter,[{key:'typeFormattterErrorFormatter',value:function typeFormattterErrorFormatter(context,err){context.out('\u003cpre class=\"jsondiffpatch-error\"\u003e'+err+'\u003c/pre\u003e');}},{key:'formatValue',value:function formatValue(context,value){context.out('\u003cpre\u003e'+htmlEscape(JSON.stringify(value,null,2))+'\u003c/pre\u003e');}},{key:'formatTextDiffString',value:function formatTextDiffString(context,value){var lines=this.parseTextDiff(value);context.out('\u003cul class=\"jsondiffpatch-textdiff\"\u003e');for(var i=0,l=lines.length;i\u003cl;i++){var line=lines[i];context.out('\u003cli\u003e\u003cdiv class=\"jsondiffpatch-textdiff-location\"\u003e'+('\u003cspan class=\"jsondiffpatch-textdiff-line-number\"\u003e'+line.location.line+'\u003c/span\u003e\u003cspan class=\"jsondiffpatch-textdiff-char\"\u003e'+line.location.chr+'\u003c/span\u003e\u003c/div\u003e\u003cdiv class=\"jsondiffpatch-textdiff-line\"\u003e'));var pieces=line.pieces;for(var pieceIndex=0,piecesLength=pieces.length;pieceIndex\u003cpiecesLength;pieceIndex++){var piece=pieces[pieceIndex];context.out('\u003cspan class=\"jsondiffpatch-textdiff-'+piece.type+'\"\u003e'+htmlEscape(decodeURI(piece.text))+'\u003c/span\u003e');}context.out('\u003c/div\u003e\u003c/li\u003e');}context.out('\u003c/ul\u003e');}},{key:'rootBegin',value:function rootBegin(context,type,nodeType){var nodeClass='jsondiffpatch-'+type+(nodeType?' jsondiffpatch-child-node-type-'+nodeType:'');context.out('\u003cdiv class=\"jsondiffpatch-delta '+nodeClass+'\"\u003e');}},{key:'rootEnd',value:function rootEnd(context){context.out('\u003c/div\u003e'+(context.hasArrows?'\u003cscript type=\"text/javascript\"\u003esetTimeout('+(adjustArrows.toString()+',10);\u003c/script\u003e'):''));}},{key:'nodeBegin',value:function nodeBegin(context,key,leftKey,type,nodeType){var nodeClass='jsondiffpatch-'+type+(nodeType?' jsondiffpatch-child-node-type-'+nodeType:'');context.out('\u003cli class=\"'+nodeClass+'\" data-key=\"'+leftKey+'\"\u003e'+('\u003cdiv class=\"jsondiffpatch-property-name\"\u003e'+leftKey+'\u003c/div\u003e'));}},{key:'nodeEnd',value:function nodeEnd(context){context.out('\u003c/li\u003e');}},{key:'format_unchanged',value:function format_unchanged(context,delta,left){if(typeof left==='undefined'){return;}context.out('\u003cdiv class=\"jsondiffpatch-value\"\u003e');this.formatValue(context,left);context.out('\u003c/div\u003e');}},{key:'format_movedestination',value:function format_movedestination(context,delta,left){if(typeof left==='undefined'){return;}context.out('\u003cdiv class=\"jsondiffpatch-value\"\u003e');this.formatValue(context,left);context.out('\u003c/div\u003e');}},{key:'format_node',value:function format_node(context,delta,left){var nodeType=delta._t==='a'?'array':'object';context.out('\u003cul class=\"jsondiffpatch-node jsondiffpatch-node-type-'+nodeType+'\"\u003e');this.formatDeltaChildren(context,delta,left);context.out('\u003c/ul\u003e');}},{key:'format_added',value:function format_added(context,delta){context.out('\u003cdiv class=\"jsondiffpatch-value\"\u003e');this.formatValue(context,delta[0]);context.out('\u003c/div\u003e');}},{key:'format_modified',value:function format_modified(context,delta){context.out('\u003cdiv class=\"jsondiffpatch-value jsondiffpatch-left-value\"\u003e');this.formatValue(context,delta[0]);context.out('\u003c/div\u003e'+'\u003cdiv class=\"jsondiffpatch-value jsondiffpatch-right-value\"\u003e');this.formatValue(context,delta[1]);context.out('\u003c/div\u003e');}},{key:'format_deleted',value:function format_deleted(context,delta){context.out('\u003cdiv class=\"jsondiffpatch-value\"\u003e');this.formatValue(context,delta[0]);context.out('\u003c/div\u003e');}},{key:'format_moved',value:function format_moved(context,delta){context.out('\u003cdiv class=\"jsondiffpatch-value\"\u003e');this.formatValue(context,delta[0]);context.out('\u003c/div\u003e\u003cdiv class=\"jsondiffpatch-moved-destination\"\u003e'+delta[1]+'\u003c/div\u003e');context.out('\u003cdiv class=\"jsondiffpatch-arrow\" '+'style=\"position: relative; left: -34px;\"\u003e\\n          \u003csvg width=\"30\" height=\"60\" '+'style=\"position: absolute; display: none;\"\u003e\\n          \u003cdefs\u003e\\n              \u003cmarker id=\"markerArrow\" markerWidth=\"8\" markerHeight=\"8\"\\n                 refx=\"2\" refy=\"4\"\\n                     orient=\"auto\" markerUnits=\"userSpaceOnUse\"\u003e\\n                  \u003cpath d=\"M1,1 L1,7 L7,4 L1,1\" style=\"fill: #339;\" /\u003e\\n              \u003c/marker\u003e\\n          \u003c/defs\u003e\\n          \u003cpath d=\"M30,0 Q-10,25 26,50\"\\n            style=\"stroke: #88f; stroke-width: 2px; fill: none; '+'stroke-opacity: 0.5; marker-end: url(#markerArrow);\"\\n          \u003e\u003c/path\u003e\\n          \u003c/svg\u003e\\n      \u003c/div\u003e');context.hasArrows=true;}},{key:'format_textdiff',value:function format_textdiff(context,delta){context.out('\u003cdiv class=\"jsondiffpatch-value\"\u003e');this.formatTextDiffString(context,delta[0]);context.out('\u003c/div\u003e');}}]);return HtmlFormatter;}(BaseFormatter);function htmlEscape(text){var html=text;var replacements=[[/\u0026/g,'\u0026amp;'],[/\u003c/g,'\u0026lt;'],[/\u003e/g,'\u0026gt;'],[/'/g,'\u0026apos;'],[/\"/g,'\u0026quot;']];for(var i=0;i\u003creplacements.length;i++){html=html.replace(replacements[i][0],replacements[i][1]);}return html;}var adjustArrows=function jsondiffpatchHtmlFormatterAdjustArrows(nodeArg){var node=nodeArg||document;var getElementText=function getElementText(_ref){var textContent=_ref.textContent,innerText=_ref.innerText;return textContent||innerText;};var eachByQuery=function eachByQuery(el,query,fn){var elems=el.querySelectorAll(query);for(var i=0,l=elems.length;i\u003cl;i++){fn(elems[i]);}};var eachChildren=function eachChildren(_ref2,fn){var children=_ref2.children;for(var i=0,l=children.length;i\u003cl;i++){fn(children[i],i);}};eachByQuery(node,'.jsondiffpatch-arrow',function(_ref3){var parentNode=_ref3.parentNode,children=_ref3.children,style=_ref3.style;var arrowParent=parentNode;var svg=children[0];var path=svg.children[1];svg.style.display='none';var destination=getElementText(arrowParent.querySelector('.jsondiffpatch-moved-destination'));var container=arrowParent.parentNode;var destinationElem=void 0;eachChildren(container,function(child){if(child.getAttribute('data-key')===destination){destinationElem=child;}});if(!destinationElem){return;}try{var distance=destinationElem.offsetTop-arrowParent.offsetTop;svg.setAttribute('height',Math.abs(distance)+6);style.top=-8+(distance\u003e0?0:distance)+'px';var curve=distance\u003e0?'M30,0 Q-10,'+Math.round(distance/2)+' 26,'+(distance-4):'M30,'+-distance+' Q-10,'+Math.round(-distance/2)+' 26,4';path.setAttribute('d',curve);svg.style.display='';}catch(err){}});};var showUnchanged=function showUnchanged(show,node,delay){var el=node||document.body;var prefix='jsondiffpatch-unchanged-';var classes={showing:prefix+'showing',hiding:prefix+'hiding',visible:prefix+'visible',hidden:prefix+'hidden'};var list=el.classList;if(!list){return;}if(!delay){list.remove(classes.showing);list.remove(classes.hiding);list.remove(classes.visible);list.remove(classes.hidden);if(show===false){list.add(classes.hidden);}return;}if(show===false){list.remove(classes.showing);list.add(classes.visible);setTimeout(function(){list.add(classes.hiding);},10);}else{list.remove(classes.hiding);list.add(classes.showing);list.remove(classes.hidden);}var intervalId=setInterval(function(){adjustArrows(el);},100);setTimeout(function(){list.remove(classes.showing);list.remove(classes.hiding);if(show===false){list.add(classes.hidden);list.remove(classes.visible);}else{list.add(classes.visible);list.remove(classes.hidden);}setTimeout(function(){list.remove(classes.visible);clearInterval(intervalId);},delay+400);},delay);};var hideUnchanged=function hideUnchanged(node,delay){return showUnchanged(false,node,delay);};var defaultInstance=void 0;function format(delta,left){if(!defaultInstance){defaultInstance=new HtmlFormatter();}return defaultInstance.format(delta,left);}var html=Object.freeze({showUnchanged:showUnchanged,hideUnchanged:hideUnchanged,default:HtmlFormatter,format:format});var AnnotatedFormatter=function(_BaseFormatter){inherits(AnnotatedFormatter,_BaseFormatter);function AnnotatedFormatter(){classCallCheck(this,AnnotatedFormatter);var _this=possibleConstructorReturn(this,(AnnotatedFormatter.__proto__||Object.getPrototypeOf(AnnotatedFormatter)).call(this));_this.includeMoveDestinations=false;return _this;}createClass(AnnotatedFormatter,[{key:'prepareContext',value:function prepareContext(context){get(AnnotatedFormatter.prototype.__proto__||Object.getPrototypeOf(AnnotatedFormatter.prototype),'prepareContext',this).call(this,context);context.indent=function(levels){this.indentLevel=(this.indentLevel||0)+(typeof levels==='undefined'?1:levels);this.indentPad=new Array(this.indentLevel+1).join('\u0026nbsp;\u0026nbsp;');};context.row=function(json,htmlNote){context.out('\u003ctr\u003e\u003ctd style=\"white-space: nowrap;\"\u003e'+'\u003cpre class=\"jsondiffpatch-annotated-indent\"'+' style=\"display: inline-block\"\u003e');context.out(context.indentPad);context.out('\u003c/pre\u003e\u003cpre style=\"display: inline-block\"\u003e');context.out(json);context.out('\u003c/pre\u003e\u003c/td\u003e\u003ctd class=\"jsondiffpatch-delta-note\"\u003e\u003cdiv\u003e');context.out(htmlNote);context.out('\u003c/div\u003e\u003c/td\u003e\u003c/tr\u003e');};}},{key:'typeFormattterErrorFormatter',value:function typeFormattterErrorFormatter(context,err){context.row('','\u003cpre class=\"jsondiffpatch-error\"\u003e'+err+'\u003c/pre\u003e');}},{key:'formatTextDiffString',value:function formatTextDiffString(context,value){var lines=this.parseTextDiff(value);context.out('\u003cul class=\"jsondiffpatch-textdiff\"\u003e');for(var i=0,l=lines.length;i\u003cl;i++){var line=lines[i];context.out('\u003cli\u003e\u003cdiv class=\"jsondiffpatch-textdiff-location\"\u003e'+('\u003cspan class=\"jsondiffpatch-textdiff-line-number\"\u003e'+line.location.line+'\u003c/span\u003e\u003cspan class=\"jsondiffpatch-textdiff-char\"\u003e'+line.location.chr+'\u003c/span\u003e\u003c/div\u003e\u003cdiv class=\"jsondiffpatch-textdiff-line\"\u003e'));var pieces=line.pieces;for(var pieceIndex=0,piecesLength=pieces.length;pieceIndex\u003cpiecesLength;pieceIndex++){var piece=pieces[pieceIndex];context.out('\u003cspan class=\"jsondiffpatch-textdiff-'+piece.type+'\"\u003e'+piece.text+'\u003c/span\u003e');}context.out('\u003c/div\u003e\u003c/li\u003e');}context.out('\u003c/ul\u003e');}},{key:'rootBegin',value:function rootBegin(context,type,nodeType){context.out('\u003ctable class=\"jsondiffpatch-annotated-delta\"\u003e');if(type==='node'){context.row('{');context.indent();}if(nodeType==='array'){context.row('\"_t\": \"a\",','Array delta (member names indicate array indices)');}}},{key:'rootEnd',value:function rootEnd(context,type){if(type==='node'){context.indent(-1);context.row('}');}context.out('\u003c/table\u003e');}},{key:'nodeBegin',value:function nodeBegin(context,key,leftKey,type,nodeType){context.row('\u0026quot;'+key+'\u0026quot;: {');if(type==='node'){context.indent();}if(nodeType==='array'){context.row('\"_t\": \"a\",','Array delta (member names indicate array indices)');}}},{key:'nodeEnd',value:function nodeEnd(context,key,leftKey,type,nodeType,isLast){if(type==='node'){context.indent(-1);}context.row('}'+(isLast?'':','));}},{key:'format_unchanged',value:function format_unchanged(){}},{key:'format_movedestination',value:function format_movedestination(){}},{key:'format_node',value:function format_node(context,delta,left){this.formatDeltaChildren(context,delta,left);}}]);return AnnotatedFormatter;}(BaseFormatter);var wrapPropertyName=function wrapPropertyName(name){return'\u003cpre style=\"display:inline-block\"\u003e\u0026quot;'+name+'\u0026quot;\u003c/pre\u003e';};var deltaAnnotations={added:function added(delta,left,key,leftKey){var formatLegend=' \u003cpre\u003e([newValue])\u003c/pre\u003e';if(typeof leftKey==='undefined'){return'new value'+formatLegend;}if(typeof leftKey==='number'){return'insert at index '+leftKey+formatLegend;}return'add property '+wrapPropertyName(leftKey)+formatLegend;},modified:function modified(delta,left,key,leftKey){var formatLegend=' \u003cpre\u003e([previousValue, newValue])\u003c/pre\u003e';if(typeof leftKey==='undefined'){return'modify value'+formatLegend;}if(typeof leftKey==='number'){return'modify at index '+leftKey+formatLegend;}return'modify property '+wrapPropertyName(leftKey)+formatLegend;},deleted:function deleted(delta,left,key,leftKey){var formatLegend=' \u003cpre\u003e([previousValue, 0, 0])\u003c/pre\u003e';if(typeof leftKey==='undefined'){return'delete value'+formatLegend;}if(typeof leftKey==='number'){return'remove index '+leftKey+formatLegend;}return'delete property '+wrapPropertyName(leftKey)+formatLegend;},moved:function moved(delta,left,key,leftKey){return'move from \u003cspan title=\"(position to remove at original state)\"\u003e'+('index '+leftKey+'\u003c/span\u003e to \u003cspan title=\"(position to insert at final')+(' state)\"\u003eindex '+delta[1]+'\u003c/span\u003e');},textdiff:function textdiff(delta,left,key,leftKey){var location=typeof leftKey==='undefined'?'':typeof leftKey==='number'?' at index '+leftKey:' at property '+wrapPropertyName(leftKey);return'text diff'+location+', format is \u003ca href=\"https://code.google.com/'+'p/google-diff-match-patch/wiki/Unidiff\"\u003ea variation of Unidiff\u003c/a\u003e';}};var formatAnyChange=function formatAnyChange(context,delta){var deltaType=this.getDeltaType(delta);var annotator=deltaAnnotations[deltaType];var htmlNote=annotator\u0026\u0026annotator.apply(annotator,Array.prototype.slice.call(arguments,1));var json=JSON.stringify(delta,null,2);if(deltaType==='textdiff'){json=json.split('\\\\n').join('\\\\n\"+\\n   \"');}context.indent();context.row(json,htmlNote);context.indent(-1);};AnnotatedFormatter.prototype.format_added=formatAnyChange;AnnotatedFormatter.prototype.format_modified=formatAnyChange;AnnotatedFormatter.prototype.format_deleted=formatAnyChange;AnnotatedFormatter.prototype.format_moved=formatAnyChange;AnnotatedFormatter.prototype.format_textdiff=formatAnyChange;var defaultInstance$1=void 0;function format$1(delta,left){if(!defaultInstance$1){defaultInstance$1=new AnnotatedFormatter();}return defaultInstance$1.format(delta,left);}var annotated=Object.freeze({default:AnnotatedFormatter,format:format$1});var OPERATIONS={add:'add',remove:'remove',replace:'replace',move:'move'};var JSONFormatter=function(_BaseFormatter){inherits(JSONFormatter,_BaseFormatter);function JSONFormatter(){classCallCheck(this,JSONFormatter);var _this=possibleConstructorReturn(this,(JSONFormatter.__proto__||Object.getPrototypeOf(JSONFormatter)).call(this));_this.includeMoveDestinations=true;return _this;}createClass(JSONFormatter,[{key:'prepareContext',value:function prepareContext(context){get(JSONFormatter.prototype.__proto__||Object.getPrototypeOf(JSONFormatter.prototype),'prepareContext',this).call(this,context);context.result=[];context.path=[];context.pushCurrentOp=function(obj){var op=obj.op,value=obj.value;var val={op:op,path:this.currentPath()};if(typeof value!=='undefined'){val.value=value;}this.result.push(val);};context.pushMoveOp=function(to){var from=this.currentPath();this.result.push({op:OPERATIONS.move,from:from,path:this.toPath(to)});};context.currentPath=function(){return'/'+this.path.join('/');};context.toPath=function(toPath){var to=this.path.slice();to[to.length-1]=toPath;return'/'+to.join('/');};}},{key:'typeFormattterErrorFormatter',value:function typeFormattterErrorFormatter(context,err){context.out('[ERROR] '+err);}},{key:'rootBegin',value:function rootBegin(){}},{key:'rootEnd',value:function rootEnd(){}},{key:'nodeBegin',value:function nodeBegin(_ref,key,leftKey){var path=_ref.path;path.push(leftKey);}},{key:'nodeEnd',value:function nodeEnd(_ref2){var path=_ref2.path;path.pop();}},{key:'format_unchanged',value:function format_unchanged(){}},{key:'format_movedestination',value:function format_movedestination(){}},{key:'format_node',value:function format_node(context,delta,left){this.formatDeltaChildren(context,delta,left);}},{key:'format_added',value:function format_added(context,delta){context.pushCurrentOp({op:OPERATIONS.add,value:delta[0]});}},{key:'format_modified',value:function format_modified(context,delta){context.pushCurrentOp({op:OPERATIONS.replace,value:delta[1]});}},{key:'format_deleted',value:function format_deleted(context){context.pushCurrentOp({op:OPERATIONS.remove});}},{key:'format_moved',value:function format_moved(context,delta){var to=delta[1];context.pushMoveOp(to);}},{key:'format_textdiff',value:function format_textdiff(){throw new Error('Not implemented');}},{key:'format',value:function format(delta,left){var context={};this.prepareContext(context);this.recurse(context,delta,left);return context.result;}}]);return JSONFormatter;}(BaseFormatter);var last=function last(arr){return arr[arr.length-1];};var sortBy=function sortBy(arr,pred){arr.sort(pred);return arr;};var compareByIndexDesc=function compareByIndexDesc(indexA,indexB){var lastA=parseInt(indexA,10);var lastB=parseInt(indexB,10);if(!(isNaN(lastA)||isNaN(lastB))){return lastB-lastA;}else{return 0;}};var opsByDescendingOrder=function opsByDescendingOrder(removeOps){return sortBy(removeOps,function(a,b){var splitA=a.path.split('/');var splitB=b.path.split('/');if(splitA.length!==splitB.length){return splitA.length-splitB.length;}else{return compareByIndexDesc(last(splitA),last(splitB));}});};var partitionOps=function partitionOps(arr,fns){var initArr=Array(fns.length+1).fill().map(function(){return[];});return arr.map(function(item){var position=fns.map(function(fn){return fn(item);}).indexOf(true);if(position\u003c0){position=fns.length;}return{item:item,position:position};}).reduce(function(acc,item){acc[item.position].push(item.item);return acc;},initArr);};var isMoveOp=function isMoveOp(_ref3){var op=_ref3.op;return op==='move';};var isRemoveOp=function isRemoveOp(_ref4){var op=_ref4.op;return op==='remove';};var reorderOps=function reorderOps(diff){var _partitionOps=partitionOps(diff,[isMoveOp,isRemoveOp]),_partitionOps2=slicedToArray(_partitionOps,3),moveOps=_partitionOps2[0],removedOps=_partitionOps2[1],restOps=_partitionOps2[2];var removeOpsReverse=opsByDescendingOrder(removedOps);return[].concat(toConsumableArray(removeOpsReverse),toConsumableArray(moveOps),toConsumableArray(restOps));};var defaultInstance$2=void 0;var format$2=function format(delta,left){if(!defaultInstance$2){defaultInstance$2=new JSONFormatter();}return reorderOps(defaultInstance$2.format(delta,left));};var log=function log(delta,left){console.log(format$2(delta,left));};var jsonpatch=Object.freeze({default:JSONFormatter,partitionOps:partitionOps,format:format$2,log:log});function chalkColor(name){return chalk\u0026\u0026chalk[name]||function(){for(var _len=arguments.length,args=Array(_len),_key=0;_key\u003c_len;_key++){args[_key]=arguments[_key];}return args;};}var colors={added:chalkColor('green'),deleted:chalkColor('red'),movedestination:chalkColor('gray'),moved:chalkColor('yellow'),unchanged:chalkColor('gray'),error:chalkColor('white.bgRed'),textDiffLine:chalkColor('gray')};var ConsoleFormatter=function(_BaseFormatter){inherits(ConsoleFormatter,_BaseFormatter);function ConsoleFormatter(){classCallCheck(this,ConsoleFormatter);var _this=possibleConstructorReturn(this,(ConsoleFormatter.__proto__||Object.getPrototypeOf(ConsoleFormatter)).call(this));_this.includeMoveDestinations=false;return _this;}createClass(ConsoleFormatter,[{key:'prepareContext',value:function prepareContext(context){get(ConsoleFormatter.prototype.__proto__||Object.getPrototypeOf(ConsoleFormatter.prototype),'prepareContext',this).call(this,context);context.indent=function(levels){this.indentLevel=(this.indentLevel||0)+(typeof levels==='undefined'?1:levels);this.indentPad=new Array(this.indentLevel+1).join('  ');this.outLine();};context.outLine=function(){this.buffer.push('\\n'+(this.indentPad||''));};context.out=function(){for(var _len2=arguments.length,args=Array(_len2),_key2=0;_key2\u003c_len2;_key2++){args[_key2]=arguments[_key2];}for(var i=0,l=args.length;i\u003cl;i++){var lines=args[i].split('\\n');var text=lines.join('\\n'+(this.indentPad||''));if(this.color\u0026\u0026this.color[0]){text=this.color[0](text);}this.buffer.push(text);}};context.pushColor=function(color){this.color=this.color||[];this.color.unshift(color);};context.popColor=function(){this.color=this.color||[];this.color.shift();};}},{key:'typeFormattterErrorFormatter',value:function typeFormattterErrorFormatter(context,err){context.pushColor(colors.error);context.out('[ERROR]'+err);context.popColor();}},{key:'formatValue',value:function formatValue(context,value){context.out(JSON.stringify(value,null,2));}},{key:'formatTextDiffString',value:function formatTextDiffString(context,value){var lines=this.parseTextDiff(value);context.indent();for(var i=0,l=lines.length;i\u003cl;i++){var line=lines[i];context.pushColor(colors.textDiffLine);context.out(line.location.line+','+line.location.chr+' ');context.popColor();var pieces=line.pieces;for(var pieceIndex=0,piecesLength=pieces.length;pieceIndex\u003cpiecesLength;pieceIndex++){var piece=pieces[pieceIndex];context.pushColor(colors[piece.type]);context.out(piece.text);context.popColor();}if(i\u003cl-1){context.outLine();}}context.indent(-1);}},{key:'rootBegin',value:function rootBegin(context,type,nodeType){context.pushColor(colors[type]);if(type==='node'){context.out(nodeType==='array'?'[':'{');context.indent();}}},{key:'rootEnd',value:function rootEnd(context,type,nodeType){if(type==='node'){context.indent(-1);context.out(nodeType==='array'?']':'}');}context.popColor();}},{key:'nodeBegin',value:function nodeBegin(context,key,leftKey,type,nodeType){context.pushColor(colors[type]);context.out(leftKey+': ');if(type==='node'){context.out(nodeType==='array'?'[':'{');context.indent();}}},{key:'nodeEnd',value:function nodeEnd(context,key,leftKey,type,nodeType,isLast){if(type==='node'){context.indent(-1);context.out(nodeType==='array'?']':'}'+(isLast?'':','));}if(!isLast){context.outLine();}context.popColor();}},{key:'format_unchanged',value:function format_unchanged(context,delta,left){if(typeof left==='undefined'){return;}this.formatValue(context,left);}},{key:'format_movedestination',value:function format_movedestination(context,delta,left){if(typeof left==='undefined'){return;}this.formatValue(context,left);}},{key:'format_node',value:function format_node(context,delta,left){this.formatDeltaChildren(context,delta,left);}},{key:'format_added',value:function format_added(context,delta){this.formatValue(context,delta[0]);}},{key:'format_modified',value:function format_modified(context,delta){context.pushColor(colors.deleted);this.formatValue(context,delta[0]);context.popColor();context.out(' =\u003e ');context.pushColor(colors.added);this.formatValue(context,delta[1]);context.popColor();}},{key:'format_deleted',value:function format_deleted(context,delta){this.formatValue(context,delta[0]);}},{key:'format_moved',value:function format_moved(context,delta){context.out('==\u003e '+delta[1]);}},{key:'format_textdiff',value:function format_textdiff(context,delta){this.formatTextDiffString(context,delta[0]);}}]);return ConsoleFormatter;}(BaseFormatter);var defaultInstance$3=void 0;var format$3=function format(delta,left){if(!defaultInstance$3){defaultInstance$3=new ConsoleFormatter();}return defaultInstance$3.format(delta,left);};function log$1(delta,left){console.log(format$3(delta,left));}var console$1=Object.freeze({default:ConsoleFormatter,format:format$3,log:log$1});var index=Object.freeze({base:base,html:html,annotated:annotated,jsonpatch:jsonpatch,console:console$1});function dateReviver(key,value){var parts=void 0;if(typeof value==='string'){parts=/^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2})(?:\\.(\\d*))?(Z|([+-])(\\d{2}):(\\d{2}))$/.exec(value);if(parts){return new Date(Date.UTC(+parts[1],+parts[2]-1,+parts[3],+parts[4],+parts[5],+parts[6],+(parts[7]||0)));}}return value;}function create(options){return new DiffPatcher(options);}var defaultInstance$4=void 0;function diff(){if(!defaultInstance$4){defaultInstance$4=new DiffPatcher();}return defaultInstance$4.diff.apply(defaultInstance$4,arguments);}function patch(){if(!defaultInstance$4){defaultInstance$4=new DiffPatcher();}return defaultInstance$4.patch.apply(defaultInstance$4,arguments);}function unpatch(){if(!defaultInstance$4){defaultInstance$4=new DiffPatcher();}return defaultInstance$4.unpatch.apply(defaultInstance$4,arguments);}function reverse(){if(!defaultInstance$4){defaultInstance$4=new DiffPatcher();}return defaultInstance$4.reverse.apply(defaultInstance$4,arguments);}function clone$1(){if(!defaultInstance$4){defaultInstance$4=new DiffPatcher();}return defaultInstance$4.clone.apply(defaultInstance$4,arguments);}exports.DiffPatcher=DiffPatcher;exports.formatters=index;exports.console=console$1;exports.create=create;exports.dateReviver=dateReviver;exports.diff=diff;exports.patch=patch;exports.unpatch=unpatch;exports.reverse=reverse;exports.clone=clone$1;Object.defineProperty(exports,'__esModule',{value:true});})));"</script>
    
    <script>
        
        let diffInstance;
        let oldData, newData, delta;

        
        
        try {
            oldData = JSON.parse("\"{\\\"groupList\\\":[{\\\"element\\\":[{\\\"ItemId\\\":4,\\\"ItemName\\\":\\\"蜂鸟预约问卷\\\",\\\"isLight\\\":0}],\\\"groupId\\\":2,\\\"groupName\\\":\\\"营销\\\",\\\"isLight\\\":0},{\\\"element\\\":[{\\\"ItemId\\\":5,\\\"ItemName\\\":\\\"到课(0/2)\\\",\\\"isLight\\\":0}],\\\"groupId\\\":3,\\\"groupName\\\":\\\"到课\\\",\\\"isLight\\\":0},{\\\"element\\\":[{\\\"ItemId\\\":6,\\\"ItemName\\\":\\\"完课(0/2)\\\",\\\"isLight\\\":0}],\\\"groupId\\\":4,\\\"groupName\\\":\\\"完课\\\",\\\"isLight\\\":0},{\\\"element\\\":[{\\\"ItemId\\\":7,\\\"ItemName\\\":\\\"回放(0/2)\\\",\\\"isLight\\\":0}],\\\"groupId\\\":5,\\\"groupName\\\":\\\"回放\\\",\\\"isLight\\\":0}]}\"");
            newData = JSON.parse("\"{\\\"groupList\\\":[{\\\"element\\\":[{\\\"ItemId\\\":7,\\\"ItemName\\\":\\\"回放(0/2)\\\",\\\"isLight\\\":0}],\\\"groupId\\\":5,\\\"groupName\\\":\\\"回放\\\",\\\"isLight\\\":0},{\\\"element\\\":[{\\\"ItemId\\\":4,\\\"ItemName\\\":\\\"蜂鸟预约问卷\\\",\\\"isLight\\\":0}],\\\"groupId\\\":2,\\\"groupName\\\":\\\"营销\\\",\\\"isLight\\\":0},{\\\"element\\\":[{\\\"ItemId\\\":5,\\\"ItemName\\\":\\\"到课(0/2)\\\",\\\"isLight\\\":0}],\\\"groupId\\\":3,\\\"groupName\\\":\\\"到课\\\",\\\"isLight\\\":0},{\\\"element\\\":[{\\\"ItemId\\\":6,\\\"ItemName\\\":\\\"完课(0/2)\\\",\\\"isLight\\\":0}],\\\"groupId\\\":4,\\\"groupName\\\":\\\"完课\\\",\\\"isLight\\\":0}]}\"");
        } catch (e) {
            console.error('JSON 解析失败:', e);
            oldData = {};
            newData = {};
        }
        

        
        document.addEventListener('DOMContentLoaded', function() {
            initializeDiff();
        });

        function initializeDiff() {
            
            diffInstance = jsondiffpatch.create({
                objectHash: function(obj, index) {
                    
                    if (obj && obj.id) return obj.id;
                    if (obj && obj.name) return obj.name;
                    return '$$index:' + index;
                },
                arrays: {
                    detectMove: true,
                    includeValueOnMove: false
                },
                textDiff: {
                    minLength: 60
                }
            });

            
            
            delta = diffInstance.diff(oldData, newData);

            if (delta) {
                
                renderVisualDiff();
            } else {
                document.getElementById('visual-diff').innerHTML = '<div class="no-diff"><div class="no-diff-icon">✅</div><h3>没有发现差异</h3><p>两个JSON版本完全相同</p></div>';
            }
            
        }

        function renderVisualDiff() {
            const visualDiffElement = document.getElementById('visual-diff');

            if (delta && diffInstance) {
                
                const html = jsondiffpatch.formatters.html.format(delta, oldData);
                visualDiffElement.innerHTML = html;
            } else {
                visualDiffElement.innerHTML = '<div class="no-diff"><div class="no-diff-icon">✅</div><h3>没有发现差异</h3><p>两个JSON版本完全相同</p></div>';
            }
        }

        function showVisualDiff() {
            document.getElementById('visual-diff').style.display = 'block';
            document.getElementById('raw-data').style.display = 'none';

            
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        function showRawData() {
            document.getElementById('visual-diff').style.display = 'none';
            document.getElementById('raw-data').style.display = 'block';

            
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        
        document.addEventListener('keydown', function(e) {
            if (e.key === '1') {
                showVisualDiff();
            } else if (e.key === '2') {
                showRawData();
            }
        });
    </script>
</body>
</html>
