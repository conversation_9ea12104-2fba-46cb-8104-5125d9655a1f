{{ define "desk/datadiff/diffresult.html" }}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON Diff 可视化对比</title>

    <!-- jsondiffpatch CSS -->
    <style>
        /* jsondiffpatch base styles */
        .jsondiffpatch-delta {
            font-family: 'Bitstream Vera Sans Mono', 'DejaVu Sans Mono', Monaco, Courier, monospace;
            font-size: 12px;
            margin: 0;
            padding: 0 0 0 12px;
            display: inline-block;
        }

        .jsondiffpatch-delta pre {
            font-family: 'Bitstream Vera Sans Mono', 'DejaVu Sans Mono', Monaco, Courier, monospace;
            font-size: 12px;
            margin: 0;
            padding: 0;
            display: inline-block;
        }

        ul.jsondiffpatch-delta {
            list-style-type: none;
            padding: 0 0 0 20px;
            margin: 0;
        }

        .jsondiffpatch-delta li {
            list-style-type: none;
        }

        .jsondiffpatch-added .jsondiffpatch-property-name,
        .jsondiffpatch-added .jsondiffpatch-value pre,
        .jsondiffpatch-added .jsondiffpatch-value {
            background: #bbffbb;
        }

        .jsondiffpatch-modified .jsondiffpatch-property-name,
        .jsondiffpatch-modified .jsondiffpatch-value pre,
        .jsondiffpatch-modified .jsondiffpatch-value {
            background: #ffffbb;
        }

        .jsondiffpatch-deleted .jsondiffpatch-property-name,
        .jsondiffpatch-deleted .jsondiffpatch-value pre,
        .jsondiffpatch-deleted .jsondiffpatch-value {
            background: #ffbbbb;
        }

        .jsondiffpatch-unchanged {
            color: gray;
        }

        .jsondiffpatch-movedestination {
            background: #ffffbb;
            color: #888;
        }

        .jsondiffpatch-movedestination::before {
            content: "(moved from ";
        }

        .jsondiffpatch-movedestination::after {
            content: ")";
        }

        .jsondiffpatch-moved .jsondiffpatch-value {
            background: #ffffbb;
        }

        .jsondiffpatch-moved .jsondiffpatch-property-name {
            color: #aaa;
            background: #ffffbb;
        }

        .jsondiffpatch-moved .jsondiffpatch-property-name::after {
            content: " (moved)";
        }

        /* Custom page styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .stat-total .stat-number { color: #667eea; }
        .stat-create .stat-number { color: #28a745; }
        .stat-update .stat-number { color: #ffc107; }
        .stat-delete .stat-number { color: #dc3545; }
        
        .diff-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            overflow: hidden;
            padding: 20px;
        }

        .no-diff {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .no-diff-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .no-diff h3 {
            font-size: 24px;
            margin-bottom: 10px;
            color: #333;
        }

        .diff-viewer {
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: white;
            overflow: auto;
            max-height: 80vh;
        }

        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .btn:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }

        .btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }
            
            .diff-change {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .diff-path {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 JSON Diff 可视化对比</h1>
            <p>清晰展示两个JSON版本之间的差异变化</p>
        </div>
        
        <div class="stats">
            <div class="stat-card stat-total">
                <div class="stat-number">{{ .Stats.Total }}</div>
                <div class="stat-label">总差异项</div>
            </div>
            <div class="stat-card stat-create">
                <div class="stat-number">{{ .Stats.Create }}</div>
                <div class="stat-label">新增</div>
            </div>
            <div class="stat-card stat-update">
                <div class="stat-number">{{ .Stats.Update }}</div>
                <div class="stat-label">修改</div>
            </div>
            <div class="stat-card stat-delete">
                <div class="stat-number">{{ .Stats.Delete }}</div>
                <div class="stat-label">删除</div>
            </div>
        </div>
        
        <div class="diff-container">
            {{ if .HasDiff }}
                <div class="controls">
                    <button class="btn active" onclick="showVisualDiff()">可视化对比</button>
                    <button class="btn" onclick="showRawData()">原始数据</button>
                </div>

                <div id="visual-diff" class="diff-viewer">
                    <!-- jsondiffpatch 可视化内容将在这里渲染 -->
                </div>

                <div id="raw-data" class="diff-viewer" style="display: none;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; height: 100%;">
                        <div>
                            <h4 style="padding: 10px; background: #f8f9fa; margin: 0; border-bottom: 1px solid #dee2e6;">原始数据</h4>
                            <pre style="padding: 20px; margin: 0; overflow: auto; height: calc(100% - 40px); background: #f8f9fa;">{{ .OldData }}</pre>
                        </div>
                        <div>
                            <h4 style="padding: 10px; background: #f8f9fa; margin: 0; border-bottom: 1px solid #dee2e6;">新数据</h4>
                            <pre style="padding: 20px; margin: 0; overflow: auto; height: calc(100% - 40px); background: #f8f9fa;">{{ .NewData }}</pre>
                        </div>
                    </div>
                </div>
            {{ else }}
                <div class="no-diff">
                    <div class="no-diff-icon">✅</div>
                    <h3>没有发现差异</h3>
                    <p>两个JSON版本完全相同</p>
                </div>
            {{ end }}
        </div>
    </div>

    <!-- 引入 jsondiffpatch 库 -->
    {{ if .JSLibContent }}
    <script>{{ .JSLibContent }}</script>
    {{ else }}
    <script src="https://cdn.jsdelivr.net/npm/jsondiffpatch@0.4.1/dist/jsondiffpatch.umd.min.js"></script>
    {{ end }}
    <script>
        // 全局变量
        let diffInstance;
        let oldData, newData, delta;

        // 初始化数据
        {{ if .HasDiff }}
        try {
            oldData = JSON.parse({{ printf "%q" .OldData }});
            newData = JSON.parse({{ printf "%q" .NewData }});
        } catch (e) {
            console.error('JSON 解析失败:', e);
            oldData = {};
            newData = {};
        }
        {{ else }}
        oldData = {};
        newData = {};
        {{ end }}

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeDiff();
        });

        function initializeDiff() {
            // 创建 jsondiffpatch 实例
            diffInstance = jsondiffpatch.create({
                objectHash: function(obj, index) {
                    // 为对象生成唯一标识，用于更好的数组差异检测
                    if (obj && obj.id) return obj.id;
                    if (obj && obj.name) return obj.name;
                    return '$$index:' + index;
                },
                arrays: {
                    detectMove: true,
                    includeValueOnMove: false
                },
                textDiff: {
                    minLength: 60
                }
            });

            {{ if .HasDiff }}
            // 计算差异
            delta = diffInstance.diff(oldData, newData);

            if (delta) {
                // 渲染可视化差异
                renderVisualDiff();
            } else {
                document.getElementById('visual-diff').innerHTML = '<div class="no-diff"><div class="no-diff-icon">✅</div><h3>没有发现差异</h3><p>两个JSON版本完全相同</p></div>';
            }
            {{ end }}
        }

        function renderVisualDiff() {
            const visualDiffElement = document.getElementById('visual-diff');

            if (delta && diffInstance) {
                // 使用 jsondiffpatch 的 HTML 格式化器
                const html = jsondiffpatch.formatters.html.format(delta, oldData);
                visualDiffElement.innerHTML = html;
            } else {
                visualDiffElement.innerHTML = '<div class="no-diff"><div class="no-diff-icon">✅</div><h3>没有发现差异</h3><p>两个JSON版本完全相同</p></div>';
            }
        }

        function showVisualDiff() {
            document.getElementById('visual-diff').style.display = 'block';
            document.getElementById('raw-data').style.display = 'none';

            // 更新按钮状态
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        function showRawData() {
            document.getElementById('visual-diff').style.display = 'none';
            document.getElementById('raw-data').style.display = 'block';

            // 更新按钮状态
            document.querySelectorAll('.btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === '1') {
                showVisualDiff();
            } else if (e.key === '2') {
                showRawData();
            }
        });
    </script>
</body>
</html>
{{ end }}