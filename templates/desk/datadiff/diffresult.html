{{ define "desk/datadiff/diffresult.html" }}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON Diff 可视化对比</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            line-height: 1.6;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .stat-total .stat-number { color: #667eea; }
        .stat-create .stat-number { color: #28a745; }
        .stat-update .stat-number { color: #ffc107; }
        .stat-delete .stat-number { color: #dc3545; }
        
        .diff-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .diff-item {
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
        }
        
        .diff-item:last-child {
            border-bottom: none;
        }
        
        .diff-item:hover {
            background-color: #f8f9fa;
        }
        
        .diff-header {
            padding: 16px 24px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            user-select: none;
            transition: all 0.2s ease;
        }
        
        .diff-header:hover {
            background: #e9ecef;
        }
        
        .diff-info {
            display: flex;
            align-items: center;
            flex: 1;
        }
        
        .diff-path {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-weight: 500;
            color: #2c3e50;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .diff-type {
            padding: 6px 14px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            min-width: 60px;
            text-align: center;
        }
        
        .type-create { background: #d4edda; color: #155724; }
        .type-update { background: #fff3cd; color: #856404; }
        .type-delete { background: #f8d7da; color: #721c24; }
        
        .diff-content {
            padding: 24px;
            display: none;
            background: #ffffff;
        }
        
        .diff-content.expanded {
            display: block;
            animation: slideDown 0.2s ease-out;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .diff-change {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .diff-from, .diff-to {
            padding: 16px;
            border-radius: 12px;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-break: break-word;
            border: 1px solid;
            position: relative;
        }
        
        .diff-from {
            background: #fef2f2;
            border-color: #fecaca;
        }
        
        .diff-to {
            background: #f0fdf4;
            border-color: #bbf7d0;
        }
        
        .diff-label {
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #6b7280;
        }
        
        .diff-value {
            color: #374151;
            font-size: 14px;
        }
        
        /* 空字符串样式 */
        .diff-value[data-type="empty-string"]::before {
            content: '""';
            color: #6b7280;
            font-weight: bold;
        }
        
        .diff-value[data-type="empty-string"] {
            color: #6b7280;
            font-style: italic;
        }
        
        /* nil 值样式 */
        .diff-value[data-type="nil"] {
            color: #9ca3af;
            font-style: italic;
        }
        
        .toggle-icon {
            transition: transform 0.2s ease;
            display: inline-block;
            margin-right: 8px;
        }
        
        .toggle-icon.expanded {
            transform: rotate(90deg);
        }
        
        .no-diff {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .no-diff-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .stats {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }
            
            .diff-change {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .diff-path {
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 JSON Diff 可视化对比</h1>
            <p>清晰展示两个JSON版本之间的差异变化</p>
        </div>
        
        <div class="stats">
            <div class="stat-card stat-total">
                <div class="stat-number">{{ .Stats.Total }}</div>
                <div class="stat-label">总差异项</div>
            </div>
            <div class="stat-card stat-create">
                <div class="stat-number">{{ .Stats.Create }}</div>
                <div class="stat-label">新增</div>
            </div>
            <div class="stat-card stat-update">
                <div class="stat-number">{{ .Stats.Update }}</div>
                <div class="stat-label">修改</div>
            </div>
            <div class="stat-card stat-delete">
                <div class="stat-number">{{ .Stats.Delete }}</div>
                <div class="stat-label">删除</div>
            </div>
        </div>
        
        <div class="diff-container">
            {{ if eq (len .DiffItems) 0 }}
            <div class="no-diff">
                <div class="no-diff-icon">✅</div>
                <h3>没有发现差异</h3>
                <p>两个JSON版本完全相同</p>
            </div>
            {{ else }}
                {{ range $index, $item := .DiffItems }}
                <div class="diff-item">
                    <div class="diff-header" id="diff-header-{{ $index }}" onclick="toggleDiff({{ $index }})">
                        <div class="diff-info">
                            <span class="toggle-icon" id="toggle-icon-{{ $index }}">▶</span>
                            <span class="diff-path">{{ $item.Path }}</span>
                        </div>
                        <span class="diff-type type-{{ $item.Type }}">{{ getTypeLabel $item.Type }}</span>
                    </div>
                    <div class="diff-content" id="diff-content-{{ $index }}">
                        <div class="diff-change">
                            <div class="diff-from">
                                <span class="diff-label">原值</span>
                                <div class="diff-value" data-type="{{ getValueType $item.From }}">{{ formatValue $item.From }}</div>
                            </div>
                            <div class="diff-to">
                                <span class="diff-label">新值</span>
                                <div class="diff-value" data-type="{{ getValueType $item.To }}">{{ formatValue $item.To }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                {{ end }}
            {{ end }}
        </div>
    </div>
    
    <script>
        function toggleDiff(index) {
            const content = document.getElementById('diff-content-' + index);
            const icon = document.getElementById('toggle-icon-' + index);
            const header = document.getElementById('diff-header-' + index);
            
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                icon.classList.remove('expanded');
            } else {
                content.classList.add('expanded');
                icon.classList.add('expanded');
            }
        }
        
        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // ESC键关闭所有展开的差异项
                document.querySelectorAll('.diff-content.expanded').forEach(content => {
                    content.classList.remove('expanded');
                });
                document.querySelectorAll('.toggle-icon.expanded').forEach(icon => {
                    icon.classList.remove('expanded');
                });
            }
        });
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('JSON Diff 可视化页面加载完成');
        });
    </script>
</body>
</html>
{{ end }}